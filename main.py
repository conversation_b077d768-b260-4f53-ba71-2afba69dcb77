import sys
import io

# 启动优化 - 必须在其他导入之前
if getattr(sys, 'frozen', False):
    # 打包环境优化
    import startup_optimizer  # 执行启动优化

    # 重定向输出到空设备
    sys.stdout = io.StringIO()
    sys.stderr = io.StringIO()
else:
    # 开发环境下正常配置编码
    if sys.stdout is not None:
        sys.stdout.reconfigure(encoding='utf-8')
    if sys.stderr is not None:
        sys.stderr.reconfigure(encoding='utf-8')
import os
import re
import random
# import shlex  # 暂时未使用
# import shutil  # 暂时未使用
import subprocess

if sys.platform.startswith('win'):
    # 隐藏控制台窗口的标志
    CREATE_NO_WINDOW = 0x08000000
else:
    CREATE_NO_WINDOW = 0

#from moviepy import afx, vfx, VideoFileClip, AudioFileClip
#from moviepy import concatenate_audioclips
from pathlib import Path
from PySide6.QtWidgets import (
    QApplication,
    QMainWindow,
    QFileDialog,
    QMessageBox,
    QRadioButton,
    QVBoxLayout,
    # QWidget,  # 暂时未使用
    QListView,
    QPushButton,
    QButtonGroup,
)
from PySide6.QtCore import QStringListModel, Qt, QThread, Signal  # QUrl暂时未使用
from PySide6.QtGui import QDragEnterEvent, QDropEvent, QPalette
from ui_main_window import Ui_mainWindow
from video_mixer import VideoMixer, MixingThread
from PySide6.QtCore import QSettings

# 导入资源管理模块
from resource_manager import get_photo_path, get_photo_folder_path, validate_photo_structure
from utils import sanitize_filename
from utils import get_video_metadata
# from utils import get_video_duration  # 在需要时动态导入
# from utils import get_valid_luts  # 暂时未使用
# from utils import _get_video_resolution  # 暂时未使用
# from utils import get_process_time  # 暂时未使用
# from moviepy import concatenate_audioclips  # 暂时未使用

VIDEO_BITRATE = "15000k"        # H.264码率（提高到15M，确保高质量）
GPU_ENABLED = True              # 强制开启GPU（无GPU设为False）
CPU_PRESET = "slow"             # CPU编码预设（slow/medium/fast）
GPU_PRESET = "p2"               # GPU编码预设（p0最快，p7最慢）
ENABLE_VIDEO_PREPROCESSING = True  # 启用视频预处理功能（解决编码兼容性问题）
# 定义 is_gpu_supported 函数
def is_gpu_supported():
    """检查NVIDIA GPU和CUDA支持"""
    try:
        # 检查nvidia-smi命令是否可用
        result = subprocess.run(
            ["nvidia-smi", "--query-gpu=name", "--format=csv,noheader"],
            capture_output=True,
            text=True,
            timeout=5,
            creationflags=CREATE_NO_WINDOW
        )
        if result.returncode == 0 and result.stdout.strip():
            return True
        else:
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError, Exception):
        return False

def build_gpu_ffmpeg_cmd(base_cmd, force_gpu=False):
    """
    构建GPU加速的FFmpeg命令
    :param base_cmd: 基础FFmpeg命令列表
    :param force_gpu: 是否强制使用GPU（用于测试）
    :return: 优化后的命令列表和是否使用GPU的标志
    """
    cmd = base_cmd.copy()
    use_gpu = (GPU_ENABLED and is_gpu_supported()) or force_gpu

    if use_gpu:
        # 1. 添加硬件加速解码参数（必须在第一个-i之前）
        if "-i" in cmd:
            first_i_index = cmd.index("-i")
            cmd.insert(first_i_index, "-hwaccel")
            cmd.insert(first_i_index + 1, "cuda")

        # 2. 替换视频编码器为GPU编码器
        if "-c:v" in cmd:
            codec_index = cmd.index("-c:v") + 1
            if codec_index < len(cmd):
                cmd[codec_index] = "h264_nvenc"
        else:
            # 如果没有-c:v参数，在输出文件前添加
            if len(cmd) > 0 and not cmd[-1].startswith("-"):
                cmd.insert(-1, "-c:v")
                cmd.insert(-1, "h264_nvenc")

        # 3. 添加GPU编码参数
        if "-preset" not in cmd:
            cmd.extend(["-preset", GPU_PRESET])
        if "-bf" not in cmd:
            cmd.extend(["-bf", "0"])

        return cmd, True
    else:
        # 使用CPU编码
        if "-c:v" in cmd:
            codec_index = cmd.index("-c:v") + 1
            if codec_index < len(cmd):
                cmd[codec_index] = "libx264"
        else:
            if len(cmd) > 0 and not cmd[-1].startswith("-"):
                cmd.insert(-1, "-c:v")
                cmd.insert(-1, "libx264")

        if "-preset" not in cmd:
            cmd.extend(["-preset", CPU_PRESET])
        return cmd, False


class HJMixingThread(QThread):
    """混剪处理线程"""
    progress_updated = Signal(int)
    log_updated = Signal(str)
    finished = Signal()

    def __init__(self, main_window):
        super().__init__()
        self.main_window = main_window
        self.should_stop = False

    def stop(self):
        self.should_stop = True

    def run(self):
        try:
            self.process_hj_mixing()
        except Exception as e:
            self.log_updated.emit(f"❌ 混剪过程中发生错误：{str(e)[:100]}")
        finally:
            self.finished.emit()

    def process_hj_mixing(self):
        """执行混剪处理"""
        # 获取设置参数 - 如果高级设置不可见，使用默认值
        if self.main_window.ui.HJ_groupBox_GJ.isVisible():
            # 高级设置可见，使用用户设置的值
            pd_min = self.main_window.ui.GJ_spinBox_PDmin.value()
            pd_max = self.main_window.ui.GJ_spinBox_PDmax.value()
            zsc_min = self.main_window.ui.GJ_spinBox_ZSCmin.value()
            zsc_max = self.main_window.ui.GJ_spinBox_ZSCmax.value()
            allow_repeat = self.main_window.ui.GJ_checkBox.isChecked()
            mute_original = self.main_window.ui.GJ_checkBox_2.isChecked()
        else:
            # 高级设置不可见，使用默认值
            pd_min = 3
            pd_max = 5
            zsc_min = 15
            zsc_max = 31
            allow_repeat = False
            mute_original = False

        video_count = self.main_window.ui.HJ_spinBox_3.value()

        self.log_updated.emit(f"混剪参数：片段时长{pd_min}-{pd_max}秒，总时长{zsc_min}-{zsc_max}秒，生成{video_count}个视频")

        # 初始化生成文件列表
        generated_files = []

        # 生成指定数量的混剪视频
        for i in range(video_count):
            if self.should_stop:
                break

            self.log_updated.emit(f"\n开始生成第{i+1}个混剪视频...")

            # 生成混剪视频
            output_file = self.generate_single_mix_video(i+1, pd_min, pd_max, zsc_min, zsc_max, allow_repeat, mute_original)

            if output_file:
                generated_files.append(output_file)

            # 更新进度
            progress = int((i + 1) / video_count * 100)
            self.progress_updated.emit(progress)

        self.log_updated.emit("\n所有混剪视频生成完成！")

        # 清理中间文件
        self._cleanup_temp_files()

        # 执行后处理扩展功能（只有抽帧、背景音乐、重命名）
        if generated_files:
            self.log_updated.emit("\n开始执行后处理扩展功能...")

            # 按正确顺序执行后处理功能：背景音乐(倒数第三) -> 抽帧去重(倒数第二) -> 批量重命名(最后)
            if self.main_window.ui.YY_checkBox_3.isChecked():
                self.log_updated.emit("执行背景音乐功能...")
                generated_files = self.apply_hj_background_music(generated_files)

            if self.main_window.ui.CZ_checkBox_2.isChecked():
                self.log_updated.emit("执行抽帧去重功能...")
                generated_files = self.apply_hj_frame_removal(generated_files)

            if self.main_window.ui.CMM_checkBox_6.isChecked():
                self.log_updated.emit("执行批量重命名功能...")
                generated_files = self.apply_hj_batch_rename(generated_files)

            self.log_updated.emit("\n所有后处理功能执行完成！")

    def generate_single_mix_video(self, index, pd_min, pd_max, zsc_min, zsc_max, allow_repeat, mute_original):
        """生成单个混剪视频（在混剪过程中应用转场和落款）"""
        try:
            # 重置当前视频的转场选择，确保不同视频可以使用不同转场
            if hasattr(self, 'current_video_transition'):
                delattr(self, 'current_video_transition')

            output_name = f"混剪视频_{index:03d}.mp4"
            output_path = os.path.join(self.main_window.hj_output_dir, output_name)

            self.log_updated.emit(f"正在生成：{output_name}")

            # 检查是否有足够的素材
            if not self.main_window.hj_files:
                self.log_updated.emit("❌ 没有可用的混剪素材")
                return None

            # 简化的混剪实现：随机选择素材并合并
            import random

            # 计算需要的总时长
            target_duration = random.randint(zsc_min, zsc_max)
            self.log_updated.emit(f"目标时长：{target_duration}秒")

            # 随机选择素材文件
            selected_files = []
            current_duration = 0

            available_files = self.main_window.hj_files.copy()

            while current_duration < target_duration and available_files:
                # 随机选择一个文件
                file = random.choice(available_files)

                # 随机选择片段时长
                segment_duration = random.randint(pd_min, pd_max)

                selected_files.append((file, segment_duration))
                current_duration += segment_duration

                # 如果不允许重复使用，从可用列表中移除
                if not allow_repeat:
                    available_files.remove(file)

                # 如果允许重复但没有更多文件了，重新填充列表
                if allow_repeat and not available_files:
                    available_files = self.main_window.hj_files.copy()

            if not selected_files:
                self.log_updated.emit("❌ 无法选择足够的素材")
                return None

            self.log_updated.emit(f"选择了{len(selected_files)}个片段")

            # 检查是否需要添加落款
            ending_file = None
            if self.main_window.ui.HJ_checkBox_LK.isChecked():
                ending_file = self._get_ending_file()
                if ending_file:
                    self.log_updated.emit(f"将添加落款：{os.path.basename(ending_file)}")

            # 使用FFmpeg合并视频片段（包含转场和落款）
            success = self._merge_video_segments_with_effects(
                selected_files, output_path, mute_original, ending_file
            )

            if success:
                self.log_updated.emit(f"✅ 生成完成：{output_name}")
                return output_path
            else:
                self.log_updated.emit(f"❌ 生成失败：{output_name}")
                return None

        except Exception as e:
            self.log_updated.emit(f"❌ 生成混剪视频失败：{str(e)[:100]}")
            return None

    def _get_ending_file(self):
        """获取落款文件"""
        try:
            if self.main_window.ui.LK_radioButton_duo.isChecked():
                # 多个落款模式
                lk_folder = self.main_window.ui.LK_lineEdit_3.text().strip()
                if lk_folder and os.path.exists(lk_folder):
                    import glob
                    lk_files = []
                    for ext in ['*.mp4', '*.avi', '*.mkv', '*.mov']:
                        lk_files.extend(glob.glob(os.path.join(lk_folder, ext)))
                    if lk_files:
                        import random
                        return random.choice(lk_files)
            else:
                # 单个落款模式
                if self.main_window.lk_files:
                    return self.main_window.lk_files[0]
            return None
        except Exception:
            return None

    def _merge_video_segments_with_effects(self, selected_files, output_path, mute_original, ending_file=None):
        """合并视频片段（包含转场和落款效果）"""
        try:
            import subprocess
            import os

            # Windows下隐藏控制台窗口
            CREATE_NO_WINDOW = 0x08000000 if os.name == 'nt' else 0

            # 创建临时文件列表
            temp_files = []

            # 提取视频片段
            for i, (file, duration) in enumerate(selected_files):
                temp_name = f"temp_segment_{i}.mp4"
                temp_path = os.path.join(os.path.dirname(output_path), temp_name)

                # 提取指定时长的片段
                cmd = [
                    "ffmpeg",
                    "-i", file,
                    "-t", str(duration),
                    "-c", "copy",
                    "-y",
                    temp_path
                ]

                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    encoding='utf-8',
                    creationflags=CREATE_NO_WINDOW
                )

                if result.returncode == 0:
                    temp_files.append(temp_path)
                else:
                    self.log_updated.emit(f"❌ 提取片段失败：{file}")
                    continue

            if not temp_files:
                return False

            # 如果有落款文件，需要特殊处理
            if ending_file:
                self.log_updated.emit(f"将添加落款：{os.path.basename(ending_file)}")

                # 检查落款文件是否需要预处理（确保格式一致）
                ending_temp_file = None
                try:
                    # 为落款文件创建临时处理版本，确保格式一致
                    ending_temp_name = f"temp_ending_{len(temp_files)}.mp4"
                    ending_temp_path = os.path.join(os.path.dirname(output_path), ending_temp_name)

                    # 使用copy模式快速处理落款文件
                    cmd = [
                        "ffmpeg",
                        "-i", ending_file,
                        "-c", "copy",
                        "-y",
                        ending_temp_path
                    ]

                    result = subprocess.run(
                        cmd,
                        capture_output=True,
                        text=True,
                        encoding='utf-8',
                        creationflags=CREATE_NO_WINDOW
                    )

                    if result.returncode == 0:
                        ending_temp_file = ending_temp_path
                        temp_files.append(ending_temp_file)
                        self.log_updated.emit(f"✅ 落款文件预处理完成")
                    else:
                        # 如果预处理失败，直接使用原文件
                        temp_files.append(ending_file)
                        self.log_updated.emit(f"⚠️ 落款文件预处理失败，使用原文件")
                except Exception as e:
                    # 如果出错，直接使用原文件
                    temp_files.append(ending_file)
                    self.log_updated.emit(f"⚠️ 落款文件处理出错，使用原文件：{str(e)[:50]}")

            # 检查是否启用转场
            use_transition = self.main_window.ui.HJ_checkBox_ZC.isChecked() and len(temp_files) > 1

            if use_transition:
                # 使用转场效果合并
                success = self._merge_with_transitions(temp_files, output_path, mute_original)
            else:
                # 使用简单拼接
                success = self._merge_with_concat(temp_files, output_path, mute_original)

            # 清理临时文件（包括临时片段文件和临时落款文件）
            for temp_file in temp_files:
                # 删除所有临时文件，但不删除原始落款文件
                file_name = os.path.basename(temp_file)
                if (temp_file != ending_file and
                    (file_name.startswith("temp_segment_") or file_name.startswith("temp_ending_"))):
                    try:
                        if os.path.exists(temp_file):
                            os.remove(temp_file)
                            self.log_updated.emit(f"🗑️ 清理临时文件：{file_name}")
                    except Exception as e:
                        self.log_updated.emit(f"⚠️ 清理临时文件失败：{str(e)[:50]}")

            return success

        except Exception as e:
            self.log_updated.emit(f"❌ 合并视频片段失败：{str(e)[:100]}")
            return False

    def _merge_with_transitions(self, temp_files, output_path, mute_original):
        """使用转场效果合并视频 - 简化版本，参考tab_QT的稳定实现"""
        try:
            import subprocess

            # Windows下隐藏控制台窗口
            CREATE_NO_WINDOW = 0x08000000 if os.name == 'nt' else 0

            # 清除上一个视频的转场选择，确保每个视频文件都能重新选择转场
            if hasattr(self, 'current_video_transition'):
                delattr(self, 'current_video_transition')

            # 获取转场设置
            transition_type = self.main_window.ui.HJ_comboBox_ZC.currentText()
            transition_duration = self.main_window.ui.ZC_doubleSpinBox_2.value()

            # 解析转场类型
            base_transition_effect = self.main_window._parse_transition_type(transition_type)

            self.log_updated.emit(f"转场类型: {transition_type}")
            self.log_updated.emit(f"转场基础效果: {base_transition_effect}")
            self.log_updated.emit(f"转场时长: {transition_duration}秒")

            # 检查是否使用多种转场（HJ_radioButton_y选中）
            use_multiple_transitions = self.main_window.ui.HJ_radioButton_y.isChecked()
            self.log_updated.emit(f"使用多种转场: {use_multiple_transitions}")

            # 为每个转场生成转场效果列表
            transition_effects = []
            import random

            # 定义转场效果池 - 按用户要求的完整列表
            all_transitions = ["fade", "smoothleft", "smoothright", "smoothup", "smoothdown",
                             "circleopen", "circleclose", "vertopen", "vertclose", "horzopen", "horzclose",
                             "distance", "diagtl", "diagtr", "diagbl", "diagbr", "wipeleft", "wiperight",
                             "wipeup", "wipedown", "slideleft", "slideright", "slideup", "slidedown",
                             "radial", "pixelize", "zoomin", "wipetl", "wipetr", "wipebl", "wipebr",
                             "hlslice", "hrslice", "vuslice", "vdslice", "hlwind", "hrwind", "vuwind", "vdwind"]

            soft_transitions = ["fade", "smoothleft", "smoothright", "smoothup", "smoothdown",
                              "circleopen", "circleclose", "vertopen", "vertclose", "horzopen", "horzclose",
                              "distance", "diagtl", "diagtr", "diagbl", "diagbr"]

            hard_transitions = ["wipeleft", "wiperight", "wipeup", "wipedown", "slideleft", "slideright",
                              "slideup", "slidedown", "radial", "pixelize", "zoomin", "wipetl", "wipetr",
                              "wipebl", "wipebr", "hlslice", "hrslice", "vuslice", "vdslice",
                              "hlwind", "hrwind", "vuwind", "vdwind"]

            for i in range(1, len(temp_files)):
                if use_multiple_transitions:
                    # 每段都随机：每个转场都从选择的策略中随机选择
                    if transition_type == "【【全随机】】":
                        effect = random.choice(all_transitions)
                    elif transition_type == "【【柔 · 随机】】":
                        effect = random.choice(soft_transitions)
                    elif transition_type == "【【硬 · 随机】】":
                        effect = random.choice(hard_transitions)
                    else:
                        # 非随机转场：每个转场都从相似转场中选择
                        if base_transition_effect in ["fade"]:
                            similar_effects = ["fade", "smoothleft", "smoothright", "smoothup", "smoothdown"]
                        elif base_transition_effect in ["wipeleft", "wiperight", "wipeup", "wipedown"]:
                            similar_effects = ["wipeleft", "wiperight", "wipeup", "wipedown"]
                        elif base_transition_effect in ["slideleft", "slideright", "slideup", "slidedown"]:
                            similar_effects = ["slideleft", "slideright", "slideup", "slidedown"]
                        elif base_transition_effect in ["circleopen", "circleclose"]:
                            similar_effects = ["circleopen", "circleclose", "vertopen", "vertclose", "horzopen", "horzclose"]
                        elif base_transition_effect in ["radial", "pixelize"]:
                            similar_effects = ["radial", "pixelize", "zoomin"]
                        else:
                            # 对于其他转场，使用原转场
                            similar_effects = [base_transition_effect]
                        effect = random.choice(similar_effects)
                else:
                    # 单个转场：单个视频内部用同一转场，不同视频之间可以用不同转场
                    # 注意：这里的逻辑是针对单个混剪视频内的多个片段
                    # 由于我们是在单个混剪视频的生成过程中，所以这里应该是所有片段用同一转场
                    if transition_type == "【【全随机】】":
                        if not hasattr(self, 'current_video_transition'):
                            # 为当前视频选择一个转场效果
                            self.current_video_transition = random.choice(all_transitions)
                            self.log_updated.emit(f"当前视频选择转场: {self.current_video_transition}")
                        effect = self.current_video_transition
                    elif transition_type == "【【柔 · 随机】】":
                        if not hasattr(self, 'current_video_transition'):
                            # 为当前视频选择一个转场效果
                            self.current_video_transition = random.choice(soft_transitions)
                            self.log_updated.emit(f"当前视频选择转场: {self.current_video_transition}")
                        effect = self.current_video_transition
                    elif transition_type == "【【硬 · 随机】】":
                        if not hasattr(self, 'current_video_transition'):
                            # 为当前视频选择一个转场效果
                            self.current_video_transition = random.choice(hard_transitions)
                            self.log_updated.emit(f"当前视频选择转场: {self.current_video_transition}")
                        effect = self.current_video_transition
                    else:
                        effect = base_transition_effect

                transition_effects.append(effect)
                self.log_updated.emit(f"转场 {i}: {effect}")

            # 简化实现：使用类似tab_QT的方式，逐步合并视频
            # 这样避免了复杂的多层滤镜导致的问题
            if len(temp_files) == 2:
                # 只有两个文件，直接使用tab_QT的方式
                return self._merge_two_videos_with_transition(temp_files[0], temp_files[1], output_path,
                                                            transition_effects[0], transition_duration, mute_original)
            else:
                # 多个文件，逐步合并
                return self._merge_multiple_videos_step_by_step(temp_files, output_path, transition_effects,
                                                              transition_duration, mute_original)

        except Exception as e:
            self.log_updated.emit(f"❌ 转场合并失败：{str(e)[:100]}")
            return False

    def _merge_two_videos_with_transition(self, video1, video2, output_path, transition_effect, transition_duration, mute_original):
        """合并两个视频，使用tab_QT的稳定方式 - 复用TransitionMixer"""
        try:
            # 导入TransitionMixer
            from transition_mixer import TransitionMixer

            # 查找FFmpeg路径
            from pathlib import Path
            ffmpeg_paths = [
                Path("D:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffmpeg.exe"),
                Path("C:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffmpeg.exe"),
                Path("E:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffmpeg.exe"),
                Path("F:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffmpeg.exe")
            ]

            ffmpeg_path = None
            ffprobe_path = None
            for path in ffmpeg_paths:
                if path.exists():
                    ffmpeg_path = path
                    ffprobe_path = path.parent / "ffprobe.exe"
                    break

            if not ffmpeg_path or not ffprobe_path.exists():
                self.log_updated.emit("❌ 未找到FFmpeg或FFprobe")
                return False

            # 创建TransitionMixer实例
            mixer = TransitionMixer(ffmpeg_path, ffprobe_path, self.log_updated.emit)

            # 设置响度统一（tab_HJ默认启用响度统一）
            mixer.set_loudnorm_enabled(True)  # tab_HJ启用响度统一，避免声音越来越响

            # 使用TransitionMixer进行转场合并
            success = mixer.merge_two_videos_with_transition(
                Path(video1), Path(video2), Path(output_path),
                transition_effect, transition_duration, mute_original
            )

            return success

        except Exception as e:
            self.log_updated.emit(f"❌ 两视频转场失败：{str(e)[:100]}")
            return False

    def _merge_multiple_videos_step_by_step(self, temp_files, output_path, transition_effects, transition_duration, mute_original):
        """逐步合并多个视频，避免复杂滤镜导致的问题"""
        try:
            self.log_updated.emit(f"开始逐步合并{len(temp_files)}个视频")

            # 如果只有一个文件，直接复制
            if len(temp_files) == 1:
                import shutil
                shutil.copy2(temp_files[0], output_path)
                return True

            # 逐步合并：A+B=AB, AB+C=ABC, ABC+D=ABCD
            current_file = temp_files[0]

            for i in range(1, len(temp_files)):
                # 创建临时输出文件
                temp_output = os.path.join(os.path.dirname(output_path), f"step_{i}.mp4")

                # 获取当前转场效果
                transition_effect = transition_effects[i-1]

                self.log_updated.emit(f"步骤 {i}: 合并 {os.path.basename(current_file)} + {os.path.basename(temp_files[i])}")

                # 使用两视频合并方法
                success = self._merge_two_videos_with_transition(
                    current_file, temp_files[i], temp_output,
                    transition_effect, transition_duration, mute_original
                )

                if not success:
                    self.log_updated.emit(f"❌ 步骤 {i} 合并失败")
                    return False

                # 清理上一步的临时文件（但不删除原始文件）
                if i > 1 and current_file.startswith(os.path.join(os.path.dirname(output_path), "step_")):
                    try:
                        os.remove(current_file)
                        self.log_updated.emit(f"🗑️ 清理中间文件：{os.path.basename(current_file)}")
                    except:
                        pass

                current_file = temp_output

            # 将最终结果移动到目标位置
            import shutil
            shutil.move(current_file, output_path)

            self.log_updated.emit(f"✅ 逐步合并完成：{os.path.basename(output_path)}")
            return True

        except Exception as e:
            self.log_updated.emit(f"❌ 逐步合并失败：{str(e)[:100]}")
            return False

    def _merge_with_concat(self, temp_files, output_path, mute_original):
        """使用简单拼接合并视频"""
        try:
            import subprocess

            # Windows下隐藏控制台窗口
            CREATE_NO_WINDOW = 0x08000000 if os.name == 'nt' else 0

            # 创建concat文件
            concat_file = os.path.join(os.path.dirname(output_path), "concat_list.txt")
            with open(concat_file, 'w', encoding='utf-8') as f:
                for temp_file in temp_files:
                    f.write(f"file '{temp_file}'\n")

            # 合并所有片段
            cmd = [
                "ffmpeg",
                "-f", "concat",
                "-safe", "0",
                "-i", concat_file,
                "-c", "copy",
                "-y",
                output_path
            ]

            # 如果需要静音
            if mute_original:
                cmd = [
                    "ffmpeg",
                    "-f", "concat",
                    "-safe", "0",
                    "-i", concat_file,
                    "-c:v", "copy",
                    "-an",  # 移除音频
                    "-y",
                    output_path
                ]

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding='utf-8',
                creationflags=CREATE_NO_WINDOW
            )

            # 清理concat文件
            try:
                os.remove(concat_file)
            except:
                pass

            return result.returncode == 0

        except Exception as e:
            self.log_updated.emit(f"❌ 拼接合并失败：{str(e)[:100]}")
            return False



    def apply_hj_background_music(self, files):
        """应用背景音乐"""
        if not self.main_window.ui.YY_checkBox_3.isChecked():
            return files

        try:
            music_folder = self.main_window.ui.YY_lineEdit_2.text().strip()
            if not music_folder or not os.path.exists(music_folder):
                self.log_updated.emit("❌ 音乐文件夹路径无效")
                return files

            # 获取音乐文件
            import glob
            music_files = []
            for ext in ['*.mp3', '*.wav', '*.aac', '*.m4a']:
                music_files.extend(glob.glob(os.path.join(music_folder, ext)))

            if not music_files:
                self.log_updated.emit("❌ 音乐文件夹中没有找到音乐文件")
                return files

            processed_files = []
            for file in files:
                # 随机选择一个音乐文件
                import random
                music_file = random.choice(music_files)

                output_name = f"music_{os.path.basename(file)}"
                output_path = os.path.join(self.main_window.hj_output_dir, output_name)

                # 使用FFmpeg添加背景音乐
                success = self._add_background_music_to_video(file, music_file, output_path)

                if success:
                    processed_files.append(output_path)
                    self.log_updated.emit(f"✅ 添加背景音乐：{output_name}")
                else:
                    processed_files.append(file)
                    self.log_updated.emit(f"❌ 添加背景音乐失败：{os.path.basename(file)}")

            return processed_files

        except Exception as e:
            self.log_updated.emit(f"❌ 背景音乐处理失败：{str(e)[:100]}")
            return files

    def apply_hj_frame_removal(self, files):
        """应用抽帧去重"""
        if not self.main_window.ui.CZ_checkBox_2.isChecked():
            return files

        try:
            low_frames = self.main_window.ui.CZ_spinBox_low_2.value()
            max_frames = self.main_window.ui.CZ_spinBox_max_2.value()

            self.log_updated.emit(f"开始抽帧去重：删除{low_frames}-{max_frames}帧")

            processed_files = []
            for file in files:
                output_name = f"frame_removed_{os.path.basename(file)}"
                output_path = os.path.join(self.main_window.hj_output_dir, output_name)

                # 使用FFmpeg进行抽帧处理
                success = self._remove_frames_from_video(file, output_path, low_frames, max_frames)

                if success:
                    processed_files.append(output_path)
                    self.log_updated.emit(f"✅ 抽帧完成：{output_name}")
                else:
                    processed_files.append(file)
                    self.log_updated.emit(f"❌ 抽帧失败：{os.path.basename(file)}")

            return processed_files

        except Exception as e:
            self.log_updated.emit(f"❌ 抽帧处理失败：{str(e)[:100]}")
            return files

    def apply_hj_batch_rename(self, files):
        """应用批量重命名"""
        if not self.main_window.ui.CMM_checkBox_6.isChecked():
            return files

        try:
            start_value = self.main_window.ui.CMM_spinBox_3.value()
            prefix = self.main_window.ui.CMM_lineEdit_5.text()
            suffix = self.main_window.ui.CMM_lineEdit_6.text()
            fill_zeros = self.main_window.ui.spinBox_3.value()
            keep_extension = self.main_window.ui.CMM_checkBox_7.isChecked()

            self.log_updated.emit(f"开始批量重命名：{prefix}[数字]{suffix}")

            processed_files = []
            for i, file in enumerate(files, start=start_value):
                file_dir = os.path.dirname(file)
                original_ext = os.path.splitext(file)[1] if keep_extension else ".mp4"
                new_name = f"{prefix}{str(i).zfill(fill_zeros)}{suffix}{original_ext}"
                new_path = os.path.join(file_dir, new_name)

                try:
                    os.rename(file, new_path)
                    processed_files.append(new_path)
                    self.log_updated.emit(f"✅ 重命名：{os.path.basename(file)} → {new_name}")
                except Exception as e:
                    processed_files.append(file)
                    self.log_updated.emit(f"❌ 重命名失败：{str(e)[:50]}")

            return processed_files

        except Exception as e:
            self.log_updated.emit(f"❌ 批量重命名失败：{str(e)[:100]}")
            return files



    def _add_background_music_to_video(self, video_file, music_file, output_path):
        """为视频添加背景音乐"""
        try:
            import subprocess

            # Windows下隐藏控制台窗口
            CREATE_NO_WINDOW = 0x08000000 if os.name == 'nt' else 0

            # 检查是否保留原音频（高级设置不可见或GJ_checkBox_2未勾选时保留）
            keep_original_audio = True
            if self.main_window.ui.HJ_groupBox_GJ.isVisible():
                keep_original_audio = not self.main_window.ui.GJ_checkBox_2.isChecked()

            if keep_original_audio:
                # 保留原音频，与背景音乐混合，先响度统一再音量调节
                cmd = [
                    "ffmpeg",
                    "-i", video_file,
                    "-i", music_file,
                    "-filter_complex", "[0:a]loudnorm=I=-23:LRA=7:TP=-2:measured_I=-23:measured_LRA=7:measured_TP=-2:measured_thresh=-34:offset=0,volume=0.8[a0];[1:a]loudnorm=I=-23:LRA=7:TP=-2:measured_I=-23:measured_LRA=7:measured_TP=-2:measured_thresh=-34:offset=0,volume=0.6[a1];[a0][a1]amix=inputs=2:duration=first:normalize=0[aout]",
                    "-map", "0:v:0",
                    "-map", "[aout]",
                    "-c:v", "copy",
                    "-c:a", "aac",
                    "-b:a", "256k",  # 提高音频码率，减少失真
                    "-ar", "48000",  # 使用48kHz采样率，提高音质
                    "-shortest",
                    "-y",
                    output_path
                ]
                self.log_updated.emit(f"🎵 保留原音频并添加背景音乐（含响度统一）")
            else:
                # 只使用背景音乐，替换原音频，先响度统一
                cmd = [
                    "ffmpeg",
                    "-i", video_file,
                    "-i", music_file,
                    "-filter_complex", "[1:a]loudnorm=I=-23:LRA=7:TP=-2:measured_I=-23:measured_LRA=7:measured_TP=-2:measured_thresh=-34:offset=0[aout]",
                    "-map", "0:v:0",
                    "-map", "[aout]",
                    "-c:v", "copy",
                    "-c:a", "aac",
                    "-shortest",
                    "-y",
                    output_path
                ]
                self.log_updated.emit(f"🎵 替换原音频为背景音乐（含响度统一）")

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding='utf-8',
                creationflags=CREATE_NO_WINDOW
            )

            # 清理原文件
            if result.returncode == 0:
                try:
                    os.remove(video_file)
                    self.log_updated.emit(f"🗑️ 清理原文件：{os.path.basename(video_file)}")
                except:
                    pass

            return result.returncode == 0

        except Exception:
            return False

    def _remove_frames_from_video(self, video_file, output_path, low_frames, max_frames):
        """从视频中移除指定帧数 - 复用tab_QT的实现"""
        try:
            import subprocess
            import random

            # Windows下隐藏控制台窗口
            CREATE_NO_WINDOW = 0x08000000 if os.name == 'nt' else 0

            # 获取视频元数据
            from utils import get_video_metadata
            meta = get_video_metadata(video_file)
            if not meta:
                self.log_updated.emit(f"❌ 无法获取视频元数据：{os.path.basename(video_file)}")
                return False

            # 检查帧数信息
            video_stream = meta.get('streams', [{}])[0]
            if 'nb_frames' not in video_stream:
                self.log_updated.emit(f"❌ 无法获取帧数信息：{os.path.basename(video_file)}")
                return False

            total_frames = int(video_stream['nb_frames'])
            delete_frames = random.randint(low_frames, max_frames)

            if total_frames < delete_frames:
                self.log_updated.emit(f"❌ 帧数不足：{os.path.basename(video_file)} ({total_frames} < {delete_frames})")
                return False

            # 随机选择要删除的帧
            frame_ids = sorted(random.sample(range(total_frames), delete_frames))
            frame_duration = float(meta["format"]["duration"]) / total_frames
            time_expr = [
                f"between(t,{f*frame_duration:.6f},{(f+1)*frame_duration:.6f})"
                for f in frame_ids
            ]

            # 构建基础FFmpeg命令
            base_cmd = [
                "ffmpeg",
                "-i", video_file,
                "-hide_banner",
                "-vf", f"select='not({'+'.join(time_expr)})',setpts=N/FRAME_RATE/TB",
                "-af", f"aselect='not({'+'.join(time_expr)})',asetpts=N/SR/TB",
                "-c:v", "placeholder",  # 将被GPU函数替换
                "-b:v", VIDEO_BITRATE,
                "-maxrate", "18000k",
                "-bufsize", "36000k",
                "-c:a", "aac",
                "-b:a", "320k",
                "-y",
                output_path
            ]

            # 使用统一的GPU加速函数
            cmd, using_gpu = build_gpu_ffmpeg_cmd(base_cmd)
            if using_gpu:
                self.log_updated.emit("🚀 使用GPU硬件加速处理")
            else:
                self.log_updated.emit("🔄 使用CPU处理")

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding='utf-8',
                creationflags=CREATE_NO_WINDOW
            )

            if result.returncode == 0:
                # 清理原文件
                try:
                    os.remove(video_file)
                except:
                    pass

                # 输出处理结果
                output_meta = get_video_metadata(output_path)
                if output_meta and 'nb_frames' in output_meta.get('streams', [{}])[0]:
                    output_frames = int(output_meta["streams"][0]["nb_frames"])
                    self.log_updated.emit(f"✅ 抽帧完成：{os.path.basename(video_file)} (原帧:{total_frames} → 新帧:{output_frames})")
                else:
                    self.log_updated.emit(f"✅ 抽帧完成：{os.path.basename(video_file)}")

            return result.returncode == 0

        except Exception as e:
            self.log_updated.emit(f"❌ 抽帧失败：{str(e)[:100]}")
            return False

    def _cleanup_temp_files(self):
        """清理中间文件"""
        try:
            output_dir = self.main_window.hj_output_dir
            if not output_dir or not os.path.exists(output_dir):
                return

            # 获取所有文件
            all_files = [
                os.path.join(output_dir, f)
                for f in os.listdir(output_dir)
                if os.path.isfile(os.path.join(output_dir, f))
            ]

            # 定义需要保留的文件模式
            keep_patterns = [
                "混剪视频_",  # 主要混剪文件
                "music_",    # 添加音乐后的文件
                "frame_removed_",  # 抽帧后的文件
            ]

            # 定义中间文件模式
            temp_patterns = [
                "temp_segment_",  # 临时片段文件
                "concat_list.txt",  # concat列表文件
            ]

            # 定义视频文件扩展名
            video_extensions = [".mp4", ".avi", ".mkv", ".mov", ".flv", ".wmv", ".MP4", ".AVI", ".MKV", ".MOV", ".FLV", ".WMV"]

            files_to_delete = []
            for file in all_files:
                file_name = os.path.basename(file)
                file_ext = os.path.splitext(file_name)[1]

                # 检查是否是中间文件
                is_temp = any(pattern in file_name for pattern in temp_patterns)

                # 检查是否是需要保留的文件
                is_keep = any(pattern in file_name for pattern in keep_patterns)

                # 检查是否是视频文件
                is_video_file = file_ext in video_extensions

                # 只删除视频文件或明确的临时文件，不删除其他类型文件（如.zip等）
                if is_temp or (not is_keep and file_name.startswith("temp_") and is_video_file):
                    files_to_delete.append(file)

            # 删除中间文件
            deleted_count = 0
            for file in files_to_delete:
                try:
                    os.remove(file)
                    deleted_count += 1
                except Exception:
                    pass

            if deleted_count > 0:
                self.log_updated.emit(f"🗑️ 清理了{deleted_count}个中间文件")

        except Exception as e:
            self.log_updated.emit(f"❌ 清理中间文件失败：{str(e)[:100]}")




class SYWatermarkThread(QThread):
    """水印处理线程"""
    log_updated = Signal(str)
    progress_updated = Signal(int)
    finished = Signal()

    def __init__(self, main_window):
        super().__init__()
        self.main_window = main_window
        self.should_stop = False

    def stop(self):
        self.should_stop = True

    def run(self):
        try:
            import random
            import glob

            # 获取FFmpeg路径
            ffmpeg_path = self.main_window.get_sy_ffmpeg_path()
            if not ffmpeg_path:
                self.log_updated.emit("❌ 未找到FFmpeg！")
                return

            self.log_updated.emit(f"🔧 使用FFmpeg：{ffmpeg_path}")

            # 获取处理参数
            video_files = self.main_window.sy_files.copy()
            replace_original = self.main_window.ui.SY_radioButton_TH_3.isChecked()
            output_dir = self.main_window.sy_output_dir if not replace_original else None

            total_files = len(video_files)
            processed_count = 0

            for video_file in video_files:
                if self.should_stop:
                    break

                try:
                    # 确定输出路径
                    if replace_original:
                        output_path = video_file  # 替换原文件
                        temp_output = video_file + ".temp.mp4"
                    else:
                        filename = os.path.basename(video_file)
                        output_path = os.path.join(output_dir, filename)
                        temp_output = output_path

                    self.log_updated.emit(f"🎬 正在处理：{os.path.basename(video_file)}")

                    # 根据选择的水印类型进行处理
                    success = self._process_watermark(video_file, temp_output, ffmpeg_path)

                    if success:
                        # 如果是替换原文件模式，需要替换原文件
                        if replace_original:
                            try:
                                os.replace(temp_output, video_file)
                                self.log_updated.emit(f"✅ 完成：{os.path.basename(video_file)}")
                            except Exception as e:
                                self.log_updated.emit(f"❌ 替换文件失败：{str(e)}")
                                if os.path.exists(temp_output):
                                    os.remove(temp_output)
                        else:
                            self.log_updated.emit(f"✅ 完成：{os.path.basename(output_path)}")
                    else:
                        self.log_updated.emit(f"❌ 处理失败：{os.path.basename(video_file)}")
                        if os.path.exists(temp_output):
                            os.remove(temp_output)

                except Exception as e:
                    self.log_updated.emit(f"❌ 处理异常：{str(e)}")

                processed_count += 1
                progress = int((processed_count / total_files) * 100)
                self.progress_updated.emit(progress)

        except Exception as e:
            self.log_updated.emit(f"❌ 线程异常：{str(e)}")
        finally:
            self.finished.emit()

    def _process_watermark(self, input_video, output_video, ffmpeg_path):
        """处理水印"""
        try:
            # 第一步：根据选择的水印类型调用相应的处理函数
            first_watermark_success = False

            if self.main_window.ui.SY_radioButton_nothing.isChecked():
                # 无水印 - 直接复制原文件
                self.log_updated.emit("📄 无水印模式：直接复制原文件...")
                try:
                    import shutil
                    shutil.copy2(input_video, output_video)
                    first_watermark_success = True
                    self.log_updated.emit("✅ 文件复制完成")
                except Exception as e:
                    self.log_updated.emit(f"❌ 文件复制失败：{str(e)}")
                    first_watermark_success = False
            elif self.main_window.ui.SY_radioButton_HHLOGO_2.isChecked():
                # 动态合合LOGO (使用用户设置的不透明度)
                opacity = self.main_window.ui.SY_spinBox_ZDSZ_2.value() / 100.0
                first_watermark_success = self._add_moving_logo_watermark(
                    input_video, output_video, ffmpeg_path, opacity
                )
            elif self.main_window.ui.SY_radioButton_HHLOGO.isChecked():
                # 全屏合合LOGO (使用用户设置的不透明度)
                opacity = self.main_window.ui.SY_spinBox_ZDSZ_3.value() / 100.0
                first_watermark_success = self._overlay_image_on_video(
                    input_video, output_video, ffmpeg_path,
                    get_photo_path("HHlogo整张水印.png"),
                    opacity
                )
            elif self.main_window.ui.SY_radioButton_HHTPSJ.isChecked():
                # 预设全屏图片随机 (使用用户设置的不透明度)
                opacity = self.main_window.ui.SY_spinBox_ZDSZ_4.value() / 100.0
                first_watermark_success = self._overlay_random_image_on_video(
                    input_video, output_video, ffmpeg_path,
                    get_photo_folder_path("others"),
                    opacity
                )
            elif self.main_window.ui.SY_radioButton_HHlogoJB.isChecked():
                # 合合LOGO角标 (100%)
                first_watermark_success = self._add_logo_watermark(
                    input_video, output_video, ffmpeg_path
                )
            elif self.main_window.ui.SY_radioButton_ZD.isChecked():
                # 自定义
                custom_folder = self.main_window.ui.SY_lineEdit_6ZD.text().strip()
                opacity = self.main_window.ui.SY_spinBox_ZDSZ.value() / 100.0
                first_watermark_success = self._overlay_random_image_on_video(
                    input_video, output_video, ffmpeg_path,
                    custom_folder, opacity
                )

            # 如果第一步处理失败，直接返回
            if not first_watermark_success:
                return False

            # 第二步：检查是否需要添加AI标识水印
            if self.main_window.ui.checkBox_AI.isChecked():
                self.log_updated.emit("🤖 添加AI标识水印...")

                # 创建临时文件用于第二次水印处理
                temp_ai_output = output_video + ".ai_temp.mp4"

                # 添加AI标识水印（100%不透明度，全屏覆盖）
                ai_watermark_success = self._overlay_image_on_video(
                    output_video, temp_ai_output, ffmpeg_path,
                    get_photo_path("AI生成.png"),
                    1.0  # 100%不透明度
                )

                if ai_watermark_success:
                    # 替换原输出文件
                    try:
                        os.replace(temp_ai_output, output_video)
                        self.log_updated.emit("✅ AI标识水印添加成功")
                    except Exception as e:
                        self.log_updated.emit(f"❌ AI标识水印文件替换失败：{str(e)}")
                        if os.path.exists(temp_ai_output):
                            os.remove(temp_ai_output)
                        return False
                else:
                    self.log_updated.emit("❌ AI标识水印添加失败")
                    if os.path.exists(temp_ai_output):
                        os.remove(temp_ai_output)
                    return False

            return True

        except Exception as e:
            self.log_updated.emit(f"❌ 水印处理异常：{str(e)}")
            return False

    def _add_moving_logo_watermark(self, input_video, output_video, ffmpeg_path, opacity=0.15):
        """添加动态移动logo水印"""
        logo_image = get_photo_path("HHlogo.png")

        if not os.path.exists(logo_image):
            self.log_updated.emit(f"❌ Logo图片不存在：{logo_image}")
            return False

        # 创建随机的动态移动轨迹
        import random
        import hashlib

        # 使用视频文件名作为随机种子，确保同一视频轨迹一致，不同视频轨迹不同
        video_name = os.path.basename(input_video)
        seed = int(hashlib.md5(video_name.encode()).hexdigest()[:8], 16)
        random.seed(seed)

        logo_width = 220
        # opacity参数从外部传入，不再硬编码
        max_speed_x = random.randint(400, 800)  # 随机X轴移动范围
        max_speed_y = random.randint(400, 800)  # 随机Y轴移动范围

        # 生成随机的频率参数（0.1-1.0之间）
        freq1_x = round(random.uniform(0.1, 1.0), 2)
        freq2_x = round(random.uniform(0.1, 1.0), 2)
        freq3_x = round(random.uniform(0.1, 1.0), 2)
        freq1_y = round(random.uniform(0.1, 1.0), 2)
        freq2_y = round(random.uniform(0.1, 1.0), 2)
        freq3_y = round(random.uniform(0.1, 1.0), 2)

        # 生成随机的相位参数（0-6.28之间，即0-2π）
        phase1_x = round(random.uniform(0, 6.28), 2)
        phase2_x = round(random.uniform(0, 6.28), 2)
        phase3_x = round(random.uniform(0, 6.28), 2)
        phase1_y = round(random.uniform(0, 6.28), 2)
        phase2_y = round(random.uniform(0, 6.28), 2)
        phase3_y = round(random.uniform(0, 6.28), 2)

        # 生成随机的幅度系数
        amp1_x = round(random.uniform(0.3, 0.7), 2)  # 主要幅度
        amp2_x = round(random.uniform(0.2, 0.5), 2)  # 次要幅度
        amp3_x = round(random.uniform(0.1, 0.3), 2)  # 微调幅度
        amp1_y = round(random.uniform(0.3, 0.7), 2)
        amp2_y = round(random.uniform(0.2, 0.5), 2)
        amp3_y = round(random.uniform(0.1, 0.3), 2)

        # 随机选择运动模式
        motion_patterns = [
            # 模式1：正弦+余弦组合
            (f"(W-w)/2 + {max_speed_x*amp1_x}*sin({freq1_x}*t + {phase1_x}) + {max_speed_x*amp2_x}*cos({freq2_x}*t + {phase2_x}) + {max_speed_x*amp3_x}*sin({freq3_x}*t + {phase3_x})",
             f"(H-h)/2 + {max_speed_y*amp1_y}*cos({freq1_y}*t + {phase1_y}) + {max_speed_y*amp2_y}*sin({freq2_y}*t + {phase2_y}) + {max_speed_y*amp3_y}*cos({freq3_y}*t + {phase3_y})"),

            # 模式2：椭圆轨迹
            (f"(W-w)/2 + {max_speed_x*amp1_x}*sin({freq1_x}*t + {phase1_x})",
             f"(H-h)/2 + {max_speed_y*amp1_y}*cos({freq1_y}*t + {phase1_y})"),

            # 模式3：8字形轨迹
            (f"(W-w)/2 + {max_speed_x*amp1_x}*sin({freq1_x}*t + {phase1_x})",
             f"(H-h)/2 + {max_speed_y*amp1_y}*sin({freq1_y*2}*t + {phase1_y})"),

            # 模式4：复杂波形
            (f"(W-w)/2 + {max_speed_x*amp1_x}*sin({freq1_x}*t + {phase1_x}) + {max_speed_x*amp2_x}*sin({freq2_x*3}*t + {phase2_x})",
             f"(H-h)/2 + {max_speed_y*amp1_y}*cos({freq1_y}*t + {phase1_y}) + {max_speed_y*amp2_y}*cos({freq2_y*2}*t + {phase2_y})")
        ]

        # 随机选择一种运动模式
        x_expr, y_expr = random.choice(motion_patterns)

        # 确保logo不会移出屏幕边界
        x_expr = f"max(0, min(W-w, {x_expr}))"
        y_expr = f"max(0, min(H-h, {y_expr}))"

        # 输出轨迹信息用于调试
        self.log_updated.emit(f"🎯 生成随机轨迹 - 种子:{seed%10000}, X范围:{max_speed_x}, Y范围:{max_speed_y}")

        cmd = [
            ffmpeg_path,
            "-i", input_video,
            "-i", logo_image,
            "-filter_complex",
            f"[1:v]scale={logo_width}:-1,format=rgba,colorchannelmixer=aa={opacity}[logo];[0:v][logo]overlay=x='{x_expr}':y='{y_expr}':shortest=0",
            "-c:a", "copy",
            "-c:v", "libx264",
            "-preset", "medium",
            "-b:v", "12000k",
            "-maxrate", "15000k",
            "-bufsize", "24000k",
            "-y",
            output_video
        ]

        return self._run_ffmpeg_command(cmd)

    def _overlay_image_on_video(self, input_video, output_video, ffmpeg_path, image_path, opacity):
        """将图片覆盖到视频上"""
        if not os.path.exists(image_path):
            self.log_updated.emit(f"❌ 图片不存在：{image_path}")
            return False

        cmd = [
            ffmpeg_path,
            "-i", input_video,
            "-i", image_path,
            "-filter_complex",
            f"[1:v]scale=1080:1920,format=rgba,colorchannelmixer=aa={opacity}[watermark];[0:v][watermark]overlay=0:0:shortest=0",
            "-c:a", "copy",
            "-c:v", "libx264",
            "-preset", "medium",
            "-b:v", "12000k",
            "-maxrate", "15000k",
            "-bufsize", "24000k",
            "-y",
            output_video
        ]

        return self._run_ffmpeg_command(cmd)

    def _overlay_random_image_on_video(self, input_video, output_video, ffmpeg_path, folder_path, opacity):
        """从文件夹中随机选择图片覆盖到视频上"""
        if not os.path.exists(folder_path):
            self.log_updated.emit(f"❌ 文件夹不存在：{folder_path}")
            return False

        # 查找图片文件
        import glob
        image_patterns = ["*.png", "*.jpg", "*.jpeg", "*.bmp", "*.tiff", "*.webp"]
        image_files = []
        for pattern in image_patterns:
            image_files.extend(glob.glob(os.path.join(folder_path, pattern)))
            image_files.extend(glob.glob(os.path.join(folder_path, pattern.upper())))

        if not image_files:
            self.log_updated.emit(f"❌ 文件夹中没有找到图片文件：{folder_path}")
            return False

        # 基于视频文件名生成一致的随机选择（每个视频对应固定的图片，但不同视频选择不同图片）
        import random
        import hashlib

        # 使用视频文件名作为随机种子，确保同一视频始终选择同一张图片
        video_name = os.path.basename(input_video)
        seed = int(hashlib.md5(video_name.encode()).hexdigest()[:8], 16)
        random.seed(seed)

        # 对图片文件列表排序，确保一致性
        image_files.sort()
        selected_image = random.choice(image_files)

        self.log_updated.emit(f"🖼️ 使用图片：{os.path.basename(selected_image)} (基于视频: {video_name})")

        return self._overlay_image_on_video(input_video, output_video, ffmpeg_path, selected_image, opacity)

    def _add_logo_watermark(self, input_video, output_video, ffmpeg_path):
        """添加固定位置logo水印"""
        logo_image = get_photo_path("HHlogo.png")

        if not os.path.exists(logo_image):
            self.log_updated.emit(f"❌ Logo图片不存在：{logo_image}")
            return False

        # 根据选择的位置确定坐标
        position_map = {
            "LUP": "48:48",  # 左上
            "RUP": "W-w-48:48",  # 右上
            "LDW": "48:H-h-48",  # 左下
            "RDW": "W-w-48:H-h-48"  # 右下
        }

        # 确定选择的位置（只有在按钮启用时才检查）
        position = "LUP"  # 默认左上
        if self.main_window.ui.radioButton_LUP.isEnabled() and self.main_window.ui.radioButton_LUP.isChecked():
            position = "LUP"
        elif self.main_window.ui.radioButton_RUP.isEnabled() and self.main_window.ui.radioButton_RUP.isChecked():
            position = "RUP"
        elif self.main_window.ui.radioButton_LDW.isEnabled() and self.main_window.ui.radioButton_LDW.isChecked():
            position = "LDW"
        elif self.main_window.ui.radioButton_RDW.isEnabled() and self.main_window.ui.radioButton_RDW.isChecked():
            position = "RDW"

        self.log_updated.emit(f"🎯 使用位置: {position}")

        overlay_pos = position_map[position]
        logo_width = 220

        cmd = [
            ffmpeg_path,
            "-i", input_video,
            "-i", logo_image,
            "-filter_complex",
            f"[1:v]scale={logo_width}:-1[logo];[0:v][logo]overlay={overlay_pos}:shortest=0",
            "-c:a", "copy",
            "-c:v", "libx264",
            "-preset", "medium",
            "-b:v", "12000k",
            "-maxrate", "15000k",
            "-bufsize", "24000k",
            "-y",
            output_video
        ]

        return self._run_ffmpeg_command(cmd)

    def _run_ffmpeg_command(self, cmd):
        """执行FFmpeg命令"""
        try:
            result = subprocess.run(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                creationflags=CREATE_NO_WINDOW
            )

            if result.returncode == 0:
                return True
            else:
                self.log_updated.emit(f"❌ FFmpeg错误：{result.stderr[:200]}")
                return False

        except Exception as e:
            self.log_updated.emit(f"❌ 命令执行异常：{str(e)}")
            return False


# 自定义拖放列表视图类（支持多列表区分）
class DragDropListView(QListView):
    def __init__(self, main_window, list_type, parent=None):
        super().__init__(parent)
        self.main_window = main_window
        self.list_type = list_type  # 区分前贴/后贴列表类型
        self.setAcceptDrops(True)
        self.setDragDropMode(QListView.DropOnly)
        self.setDefaultDropAction(Qt.CopyAction)
        self.setStyleSheet(
            """
            QListView {
                border: 2px solid #ccc;
                border-radius: 4px;
                padding: 5px;
            }
            QListView::drop-indicator {
                background-color: #a6c9e2;
                border-top: 2px solid #3a6ea5;
            }
        """
        )
        # 初始化 modified_files 属性
        self.modified_files = {}  # 用于存储修改后的文件

    def dragEnterEvent(self, event: QDragEnterEvent):
        print(f"【调试】dragEnterEvent 触发 - list_type: {self.list_type}")  # 添加调试打印
        if event.mimeData().hasUrls():
            for url in event.mimeData().urls():
                if url.isLocalFile() and self._is_video_file(url.toLocalFile()):
                    event.acceptProposedAction()
                    return
        event.ignore()

    def dragMoveEvent(self, event: QDragEnterEvent):
        print("【调试】dragMoveEvent 触发")  # 添加调试打印
        if event.mimeData().hasUrls():
            for url in event.mimeData().urls():
                if url.isLocalFile() and self._is_video_file(url.toLocalFile()):
                    event.acceptProposedAction()
                    return
        event.ignore()

    def dropEvent(self, event: QDropEvent):
        print("【调试】dropEvent 触发")  # 添加调试打印
        if event.mimeData().hasUrls():
            files = [url.toLocalFile() for url in event.mimeData().urls()]
            self.main_window.add_files_from_drag(files, self.list_type)
            event.acceptProposedAction()

    def _is_video_file(self, file_path):
        video_extensions = [".mp4", ".avi", ".mkv", ".mov", ".flv", ".wmv"]
        suffix = Path(file_path).suffix.lower()
        print(f"【调试】文件 {file_path} 的后缀是：{suffix}")  # 添加调试打印
        return suffix in video_extensions


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.ui = Ui_mainWindow()
        self.ui.setupUi(self)

        # 验证photo资源文件
        self._validate_photo_resources()

        # 初始化设置
        self.settings = QSettings("YourCompany", "YourAppName")

        # 设置默认状态
        self.ui.CMM_checkBox_3.setChecked(True)  # 保留扩展名默认勾选
        self.ui.CMM_checkBox_4.setChecked(False)  # 批量重命名默认不勾选

        # 读取用户设置
        self._load_settings()

        # 确保 CMM_checkBox_4 始终为不勾选状态（不受设置影响）
        self.ui.CMM_checkBox_4.setChecked(False)

        # 初始化混剪器
        self.mixer = VideoMixer()
        self.mixer.set_log_callback(self.update_log)

        # 为tab_QT设置响度统一（默认启用，确保前贴和后贴素材响度统一）
        self.mixer.set_loudnorm_enabled(True)

        # 数据存储
        self.main_files = []  # 前贴文件
        self.variant_files = []  # 后贴文件
        self.output_dir = ""  # 输出目录
        self.selected_strategy = None
        self.is_running = False

        # 初始化抽帧tab的相关控件
        self.frame_files = []  # 存储要处理的文件
        self.frame_model = QStringListModel()  # 列表模型
        self.frame_processor = None  # 抽帧处理器实例

        # 初始化混剪tab的相关控件
        self.hj_files = []  # 存储混剪素材文件
        self.hj_model = QStringListModel()  # 混剪列表模型
        self.hj_output_dir = ""  # 混剪输出目录
        self.hj_is_running = False  # 混剪运行状态
        self.lk_files = []  # 存储落款文件
        self.lk_model = QStringListModel()  # 落款列表模型

        # 初始化洗素材tab的相关控件
        self.xsc_files = []  # 存储要洗的文件
        self.xsc_model = QStringListModel()  # 洗素材列表模型
        self.xsc_is_running = False  # 洗素材运行状态
        self.xsc_processor = None  # 洗素材处理器实例

        # 替换前贴列表视图
        #print("【调试】正在替换前贴列表视图...")  # 添加调试打印
        self.qt_list_view = DragDropListView(self, "main", self.ui.SPZD_groupBox)
        self.qt_list_view.setObjectName("QT_listView")
        self.qt_list_view.setGeometry(self.ui.QT_listView.geometry())
        #print(f"【调试】前贴列表视图原始对象: {self.ui.QT_listView}")  # 添加调试打印
        self.ui.QT_listView.deleteLater()
        self.ui.QT_listView = self.qt_list_view
        #print(f"【调试】前贴列表视图替换后对象: {self.ui.QT_listView}")  # 添加调试打印

        # 替换后贴列表视图
        #print("【调试】正在替换后贴列表视图...")  # 添加调试打印
        self.ht_list_view = DragDropListView(self, "variant", self.ui.SPZD_groupBox)
        self.ht_list_view.setObjectName("HT_listView_2")
        self.ht_list_view.setGeometry(self.ui.HT_listView_2.geometry())
        #print(f"【调试】后贴列表视图原始对象: {self.ui.HT_listView_2}")  # 添加调试打印
        self.ui.HT_listView_2.deleteLater()
        self.ui.HT_listView_2 = self.ht_list_view
        # print(
        #     f"【调试】后贴列表视图替换后对象: {self.ui.HT_listView_2}"
        # )  # 添加调试打印

        # 初始化列表模型
        self.qt_model = QStringListModel()
        self.ui.QT_listView.setModel(self.qt_model)
        self.ht_model = QStringListModel()
        self.ui.HT_listView_2.setModel(self.ht_model)

        # 按钮事件连接
        self.ui.QT_in.clicked.connect(self.add_main_files)
        self.ui.QT_out.clicked.connect(self.remove_selected_main_files)
        self.ui.QT_clean.clicked.connect(self.clear_main_files)
        self.ui.HT_in.clicked.connect(self.add_variant_files)
        self.ui.HT_out.clicked.connect(self.remove_selected_variant_files)
        self.ui.HT_clean.clicked.connect(self.clear_variant_files)
        self.ui.SC_where.clicked.connect(self.select_output_directory)
        self.ui.CSHJC.clicked.connect(self.run_initial_check)
        self.ui.KS_pushButton.clicked.connect(self.start_mix)
        self.ui.TZ_pushButton.clicked.connect(self.stop_mix)

        # 策略布局初始化
        self.strategy_layout = QVBoxLayout()
        self.strategy_buttons = []
        self.ui.CL_widget.setLayout(self.strategy_layout)
        self.ui.KS_pushButton.setEnabled(False)
        self.ui.TZ_pushButton.setEnabled(False)
        self.ui.progressBar.setValue(0)

        # 连接扩展功能控件的信号
        self.ui.CZ_spinBox_low.valueChanged.connect(self._save_settings)
        self.ui.CZ_spinBox_max.valueChanged.connect(self._save_settings)
        self.ui.YY_pushButton.clicked.connect(self._select_music_folder)
        self.ui.YY_spinBox_yyyl.valueChanged.connect(self._save_settings)
        self.ui.YY_spinBox_yspyl.valueChanged.connect(self._save_settings)
        self.ui.CMM_spinBox.valueChanged.connect(self._save_settings)
        self.ui.CMM_lineEdit.textChanged.connect(self._save_settings)
        self.ui.CMM_lineEdit_2.textChanged.connect(self._save_settings)
        self.ui.spinBox.valueChanged.connect(self._save_settings)
        self.ui.CMM_checkBox_3.setChecked(True)

        # 连接转场功能控件的信号
        self.ui.ZC_checkBox.toggled.connect(self._save_settings)
        self.ui.ZC_comboBox.currentTextChanged.connect(self._save_settings)
        self.ui.ZC_doubleSpinBox.valueChanged.connect(self._save_settings)

        # 连接竖版素材自动洗复选框信号
        self.ui.checkBox_AUTOSHU.toggled.connect(self._save_settings)

        # 替换抽帧列表视图
        self.frame_list_view = DragDropListView(self, "frame", self.ui.DDCZ_groupBox)
        self.frame_list_view.setObjectName("DDCZ_listView_3")
        self.frame_list_view.setGeometry(self.ui.DDCZ_listView_3.geometry())
        self.ui.DDCZ_listView_3.deleteLater()
        self.ui.DDCZ_listView_3 = self.frame_list_view
        self.ui.DDCZ_listView_3.setModel(self.frame_model)

        # 连接抽帧tab的信号
        self.ui.DDCZ_in_3.clicked.connect(self.add_frame_files)
        self.ui.DDCZ_out_3.clicked.connect(self.remove_selected_frame_files)
        self.ui.DDCZ_clean_3.clicked.connect(self.clear_frame_files)
        self.ui.DDCZ_where_3.clicked.connect(self.select_frame_output_directory)
        self.ui.DDCZ_pushButton_2.clicked.connect(self.start_frame_removal)
        self.ui.DDCZ_pushButton_3.clicked.connect(self.stop_frame_removal)

        # 连接单选按钮信号
        self.ui.DDCZ_radioButton_TH.toggled.connect(self.on_frame_radio_toggled)
        self.ui.DDCZ_radioButton_ZD.toggled.connect(self.on_frame_radio_toggled)

        # 设置默认选择
        self.ui.DDCZ_radioButton_ZD.setChecked(True)

        # 加载抽帧设置
        self.ui.DDCZ_spinBox_low_2.setValue(
            self.settings.value("DDCZ_spinBox_low_2", 15, type=int)
        )
        self.ui.DDCZ_spinBox_max_2.setValue(
            self.settings.value("DDCZ_spinBox_max_2", 50, type=int)
        )

        # 连接抽帧设置保存
        self.ui.DDCZ_spinBox_low_2.valueChanged.connect(self._save_settings)
        self.ui.DDCZ_spinBox_max_2.valueChanged.connect(self._save_settings)

        # 界面颜色调整功能初始化（先连接信号，后加载设置）
        self.is_dark_mode = False  # 记录当前是否为反色模式
        self.ui.checkBox.toggled.connect(self.toggle_interface_color)
        self.ui.checkBox.toggled.connect(self._save_settings)  # 保存设置

        # 连接抽帧重命名设置保存
        self.ui.CMM_spinBox_2.valueChanged.connect(self._save_settings)
        self.ui.CMM_lineEdit_3.textChanged.connect(self._save_settings)
        self.ui.CMM_lineEdit_4.textChanged.connect(self._save_settings)
        self.ui.spinBox_2.valueChanged.connect(self._save_settings)
        self.ui.CMM_checkBox_5.toggled.connect(self._save_settings)
        # CMM_checkBox_4 不保存设置，每次都默认为不勾选状态

        # 替换混剪tab的列表视图
        self.hj_list_view = DragDropListView(self, "hj", self.ui.HJSPZD_groupBox_2)
        self.hj_list_view.setObjectName("HJ_listView_4")
        self.hj_list_view.setGeometry(self.ui.HJ_listView_4.geometry())
        self.ui.HJ_listView_4.deleteLater()
        self.ui.HJ_listView_4 = self.hj_list_view
        self.ui.HJ_listView_4.setModel(self.hj_model)

        # 替换落款列表视图
        self.lk_list_view = DragDropListView(self, "lk", self.ui.HJ_groupBox_LK)
        self.lk_list_view.setObjectName("LK_listView")
        self.lk_list_view.setGeometry(self.ui.LK_listView.geometry())
        self.ui.LK_listView.deleteLater()
        self.ui.LK_listView = self.lk_list_view
        self.ui.LK_listView.setModel(self.lk_model)

        # 替换洗素材列表视图
        self.xsc_list_view = DragDropListView(self, "xsc", self.ui.XSC_groupBox_2)
        self.xsc_list_view.setObjectName("XSC_listView_4")
        self.xsc_list_view.setGeometry(self.ui.XSC_listView_4.geometry())
        self.ui.XSC_listView_4.deleteLater()
        self.ui.XSC_listView_4 = self.xsc_list_view
        self.ui.XSC_listView_4.setModel(self.xsc_model)

        # 连接混剪tab的信号
        self.ui.HJ_in_4.clicked.connect(self.add_hj_files)
        self.ui.HJ_out_4.clicked.connect(self.remove_selected_hj_files)
        self.ui.HJ_clean_4.clicked.connect(self.clear_hj_files)
        self.ui.HJSC_where_4.clicked.connect(self.select_hj_output_directory)
        self.ui.CSHJC_4.clicked.connect(self.run_hj_initial_check)
        self.ui.HJKS_pushButton_2.clicked.connect(self.start_hj_mix)
        self.ui.HJTZ_pushButton_2.clicked.connect(self.stop_hj_mix)
        self.ui.HJ_pushButton_GJ.clicked.connect(self.toggle_hj_advanced_settings)

        # 连接落款相关信号
        self.ui.LK_in_5.clicked.connect(self.add_lk_files)
        self.ui.LK_clean_5.clicked.connect(self.clear_lk_files)
        self.ui.LK_pushButton_3.clicked.connect(self.select_lk_folder)
        self.ui.LK_radioButton_duo.toggled.connect(self.on_lk_radio_toggled)
        self.ui.LK_radioButton_dan.toggled.connect(self.on_lk_radio_toggled)

        # 连接tab_HJ的背景音乐按钮
        self.ui.YY_pushButton_2.clicked.connect(self.select_hj_music_folder)

        # 连接转场相关信号
        self.ui.HJ_checkBox_ZC.toggled.connect(self.on_hj_transition_toggled)
        self.ui.HJ_comboBox_ZC.currentTextChanged.connect(self.on_hj_transition_changed)

        # 连接tab_HJ相关设置保存信号
        self.ui.CZ_spinBox_low_2.valueChanged.connect(self._save_settings)
        self.ui.CZ_spinBox_max_2.valueChanged.connect(self._save_settings)
        self.ui.YY_lineEdit_2.textChanged.connect(self._save_settings)
        self.ui.YY_spinBox_yspyl_2.valueChanged.connect(self._save_settings)
        self.ui.YY_spinBox_yyyl_2.valueChanged.connect(self._save_settings)
        self.ui.CMM_spinBox_3.valueChanged.connect(self._save_settings)
        self.ui.CMM_lineEdit_5.textChanged.connect(self._save_settings)
        self.ui.CMM_lineEdit_6.textChanged.connect(self._save_settings)
        self.ui.spinBox_3.valueChanged.connect(self._save_settings)
        self.ui.LK_lineEdit_3.textChanged.connect(self._save_settings)

        # 连接洗素材tab的信号
        self.ui.XSC_in_4.clicked.connect(self.add_xsc_files)
        self.ui.XSC_out_4.clicked.connect(self.remove_selected_xsc_files)
        self.ui.XSC_clean_4.clicked.connect(self.clear_xsc_files)
        self.ui.XSC_where_4.clicked.connect(self.select_xsc_output_directory)
        self.ui.XSC_pushButton_3.clicked.connect(self.start_xsc_processing)
        self.ui.XSC_pushButton_4.clicked.connect(self.stop_xsc_processing)

        # 连接洗素材单选按钮信号
        self.ui.XSC_radioButton_TH_2.toggled.connect(self.on_xsc_radio_toggled)
        self.ui.XSC_radioButton_ZD_2.toggled.connect(self.on_xsc_radio_toggled)

        # 连接洗素材参数设置保存信号
        self.ui.SXC_textEdit_W.textChanged.connect(self._save_settings)
        self.ui.SXC_textEdit_H.textChanged.connect(self._save_settings)
        self.ui.SXC_textEdit_BTL.textChanged.connect(self._save_settings)

        # 设置洗素材默认选择
        self.ui.XSC_radioButton_TH_2.setChecked(True)
        # 初始化洗素材控件状态
        self.on_xsc_radio_toggled()

        # ===== tab_SY 水印工具初始化 =====
        # 替换水印素材列表视图
        self.sy_list_view = DragDropListView(self, "sy", self.ui.SY_groupBox_3)
        self.sy_list_view.setObjectName("SY_listView_5")
        self.sy_list_view.setGeometry(self.ui.SY_listView_5.geometry())
        self.ui.SY_listView_5.deleteLater()
        self.ui.SY_listView_5 = self.sy_list_view
        self.sy_model = QStringListModel()
        self.ui.SY_listView_5.setModel(self.sy_model)
        self.sy_files = []  # 存储水印素材文件

        # 连接水印tab的信号
        self.ui.SY_in_5.clicked.connect(self.add_sy_files)
        self.ui.SY_out_5.clicked.connect(self.remove_selected_sy_files)
        self.ui.SY_clean_5.clicked.connect(self.clear_sy_files)
        self.ui.SY_where_5.clicked.connect(self.select_sy_output_folder)
        self.ui.SY_pushButton_5.clicked.connect(self.start_sy_processing)
        self.ui.SY_pushButton_6.clicked.connect(self.stop_sy_processing)

        # 连接水印单选按钮信号
        self.ui.SY_radioButton_TH_3.toggled.connect(self.on_sy_radio_toggled)
        self.ui.SY_radioButton_ZD_3.toggled.connect(self.on_sy_radio_toggled)

        # 连接AI标识复选框信号
        self.ui.checkBox_AI.toggled.connect(self._save_settings)

        # 连接水印指定区单选按钮信号
        self.ui.SY_radioButton_HHLOGO_2.toggled.connect(self.on_sy_watermark_radio_toggled)
        self.ui.SY_radioButton_HHLOGO.toggled.connect(self.on_sy_watermark_radio_toggled)
        self.ui.SY_radioButton_HHTPSJ.toggled.connect(self.on_sy_watermark_radio_toggled)
        self.ui.SY_radioButton_HHlogoJB.toggled.connect(self.on_sy_watermark_radio_toggled)
        self.ui.SY_radioButton_ZD.toggled.connect(self.on_sy_watermark_radio_toggled)
        self.ui.SY_radioButton_nothing.toggled.connect(self.on_sy_watermark_radio_toggled)  # 添加"无水印"信号连接

        # 连接位置选择单选按钮信号
        self.ui.radioButton_LUP.toggled.connect(self.on_sy_position_radio_toggled)
        self.ui.radioButton_RUP.toggled.connect(self.on_sy_position_radio_toggled)
        self.ui.radioButton_LDW.toggled.connect(self.on_sy_position_radio_toggled)
        self.ui.radioButton_RDW.toggled.connect(self.on_sy_position_radio_toggled)

        # 连接自定义水印设置
        self.ui.SY_where_6ZD.clicked.connect(self.select_sy_custom_folder)
        self.ui.SY_lineEdit_6ZD.textChanged.connect(self._save_settings)
        self.ui.SY_spinBox_ZDSZ.valueChanged.connect(self._save_settings)

        # 连接新增的不透明度设置
        self.ui.SY_spinBox_ZDSZ_2.valueChanged.connect(self._save_settings)  # 动态合合LOGO不透明度
        self.ui.SY_spinBox_ZDSZ_3.valueChanged.connect(self._save_settings)  # 全屏合合LOGO不透明度
        self.ui.SY_spinBox_ZDSZ_4.valueChanged.connect(self._save_settings)  # 预设全屏图片不透明度

        # 创建ButtonGroup来管理A组和B组单选按钮
        # A组：水印类型
        self.sy_watermark_group = QButtonGroup(self)
        self.sy_watermark_group.addButton(self.ui.SY_radioButton_HHLOGO_2)
        self.sy_watermark_group.addButton(self.ui.SY_radioButton_HHLOGO)
        self.sy_watermark_group.addButton(self.ui.SY_radioButton_HHTPSJ)
        self.sy_watermark_group.addButton(self.ui.SY_radioButton_HHlogoJB)
        self.sy_watermark_group.addButton(self.ui.SY_radioButton_ZD)
        self.sy_watermark_group.addButton(self.ui.SY_radioButton_nothing)  # 添加"无水印"选项

        # B组：位置选择
        self.sy_position_group = QButtonGroup(self)
        self.sy_position_group.addButton(self.ui.radioButton_LUP)
        self.sy_position_group.addButton(self.ui.radioButton_RUP)
        self.sy_position_group.addButton(self.ui.radioButton_LDW)
        self.sy_position_group.addButton(self.ui.radioButton_RDW)

        # 设置水印默认选择
        self.ui.SY_radioButton_TH_3.setChecked(True)
        self.ui.SY_radioButton_HHLOGO_2.setChecked(True)  # 默认选择动态合合LOGO
        self.ui.radioButton_LUP.setChecked(True)  # 默认左上角位置

        # 初始化水印控件状态
        self.on_sy_radio_toggled()
        self.on_sy_watermark_radio_toggled()

        # 水印处理相关变量
        self.sy_output_dir = ""
        self.sy_processing_thread = None
        self.sy_is_running = False

        # 设置默认状态
        self.ui.HJ_groupBox_GJ.setVisible(False)  # 默认隐藏高级设置
        self.ui.LK_radioButton_dan.setChecked(True)  # 默认选择单个落款
        self.ui.HJ_radioButton_n.setChecked(True)  # 默认选择只用一个转场

        # 设置混剪tab的默认值
        self.ui.GJ_spinBox_PDmin.setValue(3)  # 片段最短时长默认3秒
        self.ui.GJ_spinBox_PDmax.setValue(5)  # 片段最长时长默认5秒
        self.ui.GJ_spinBox_ZSCmin.setValue(15)  # 总时长最短默认15秒
        self.ui.GJ_spinBox_ZSCmax.setValue(31)  # 总时长最长默认31秒
        self.ui.GJ_checkBox.setChecked(False)  # 默认不重复使用素材
        self.ui.GJ_checkBox_2.setChecked(False)  # 默认不静音原素材
        self.ui.HJ_spinBox_3.setValue(1)  # 默认生成1个视频

        self.on_lk_radio_toggled()  # 初始化落款控件状态
        self.on_hj_transition_toggled()  # 初始化转场控件状态

    def change_extensions(self, files, new_ext):
        """修改文件扩展名"""
        renamed_files = []
        for file in files:
            file_dir = os.path.dirname(file)
            file_name = os.path.basename(file)
            base_name, _ = os.path.splitext(file_name)
            new_name = f"{base_name}{new_ext}"
            new_path = os.path.join(file_dir, new_name)
            try:
                os.rename(file, new_path)
                self.update_log(f"✅ 修改扩展名：{file} → {new_path}")
                renamed_files.append(new_path)
            except Exception as e:
                self.update_log(f"❌ 修改扩展名失败：{str(e)[:50]}")
                renamed_files.append(file)  # 失败时保留原文件
        return renamed_files

    def restore_extensions(self):
        """恢复所有文件的原始扩展名"""
        if not hasattr(self, 'modified_files') or not self.modified_files:
            return

        # 创建原始文件路径到临时文件路径的映射
        original_to_temp = {
            Path(f).with_suffix('.MP4'): Path(f)
            for f in self.modified_files
        }

        # 恢复扩展名
        for original_path, temp_path in original_to_temp.items():
            if temp_path.exists():
                try:
                    temp_path.rename(original_path)
                    self.update_log(f"✅ 恢复扩展名：{temp_path} → {original_path}")
                except Exception as e:
                    self.update_log(f"❌ 恢复扩展名失败：{str(e)[:50]}")
            else:
                self.update_log(f"⚠️ 文件已不存在：{temp_path}")

        # 清理临时映射
        delattr(self, 'modified_files')

    def _load_settings(self):
        # 抽帧去重设置
        self.ui.CZ_spinBox_low.setValue(
            self.settings.value("CZ_spinBox_low", 15, type=int)
        )
        self.ui.CZ_spinBox_max.setValue(
            self.settings.value("CZ_spinBox_max", 50, type=int)
        )

        # 转场功能设置 - 默认不启用
        self.ui.ZC_checkBox.setChecked(False)
        self.ui.ZC_comboBox.setCurrentText(
            self.settings.value("ZC_comboBox", "【【全随机】】")
        )
        self.ui.ZC_doubleSpinBox.setValue(
            self.settings.value("ZC_doubleSpinBox", 1.0, type=float)
        )

        # 竖版素材自动洗设置
        self.ui.checkBox_AUTOSHU.setChecked(
            self.settings.value("checkBox_AUTOSHU", False, type=bool)
        )

        # 整体添加背景音乐设置
        music_folder = self.settings.value("YY_lineEdit", "")
        self.ui.YY_lineEdit.setText(music_folder)
        self.ui.YY_spinBox_yyyl.setValue(
            self.settings.value("YY_spinBox_yyyl", 0, type=int)
        )
        self.ui.YY_spinBox_yspyl.setValue(
            self.settings.value("YY_spinBox_yspyl", 0, type=int)
        )

        # 批量重命名设置
        self.ui.CMM_spinBox.setValue(self.settings.value("CMM_spinBox", 1, type=int))
        self.ui.CMM_lineEdit.setText(self.settings.value("CMM_lineEdit", ""))
        self.ui.CMM_lineEdit_2.setText(self.settings.value("CMM_lineEdit_2", ""))
        self.ui.spinBox.setValue(self.settings.value("spinBox", 1, type=int))

        # 加载抽帧重命名设置
        self.ui.CMM_spinBox_2.setValue(
            self.settings.value("CMM_spinBox_2", 1, type=int)
        )
        self.ui.CMM_lineEdit_3.setText(
            self.settings.value("CMM_lineEdit_3", "")
        )
        self.ui.CMM_lineEdit_4.setText(
            self.settings.value("CMM_lineEdit_4", "")
        )
        self.ui.spinBox_2.setValue(
            self.settings.value("spinBox_2", 1, type=int)
        )
        self.ui.CMM_checkBox_5.setChecked(
            self.settings.value("CMM_checkBox_5", True, type=bool)
        )
        # CMM_checkBox_4 不记录用户上次选择，始终默认为不勾选状态
        self.ui.CMM_checkBox_4.setChecked(False)

        # 加载tab_HJ相关设置
        self.ui.CZ_spinBox_low_2.setValue(
            self.settings.value("CZ_spinBox_low_2", 15, type=int)
        )
        self.ui.CZ_spinBox_max_2.setValue(
            self.settings.value("CZ_spinBox_max_2", 50, type=int)
        )
        self.ui.YY_lineEdit_2.setText(
            self.settings.value("YY_lineEdit_2", "")
        )
        self.ui.YY_spinBox_yspyl_2.setValue(
            self.settings.value("YY_spinBox_yspyl_2", 5, type=int)
        )
        self.ui.YY_spinBox_yyyl_2.setValue(
            self.settings.value("YY_spinBox_yyyl_2", 3, type=int)
        )
        self.ui.CMM_spinBox_3.setValue(
            self.settings.value("CMM_spinBox_3", 1, type=int)
        )
        self.ui.CMM_lineEdit_5.setText(
            self.settings.value("CMM_lineEdit_5", "")
        )
        self.ui.CMM_lineEdit_6.setText(
            self.settings.value("CMM_lineEdit_6", "")
        )
        self.ui.spinBox_3.setValue(
            self.settings.value("spinBox_3", 3, type=int)
        )

        # 加载LK_lineEdit_3设置
        self.ui.LK_lineEdit_3.setText(
            self.settings.value("LK_lineEdit_3", "")
        )

        # 加载YY_lineEdit_2设置
        self.ui.YY_lineEdit_2.setText(
            self.settings.value("YY_lineEdit_2", "")
        )

        # 加载洗素材设置
        self.ui.SXC_textEdit_W.setPlainText(
            self.settings.value("SXC_textEdit_W", "1080")
        )
        self.ui.SXC_textEdit_H.setPlainText(
            self.settings.value("SXC_textEdit_H", "1920")
        )
        self.ui.SXC_textEdit_BTL.setPlainText(
            self.settings.value("SXC_textEdit_BTL", "12000")
        )

        # 加载水印工具设置
        self.ui.SY_lineEdit_6ZD.setText(
            self.settings.value("SY_lineEdit_6ZD", "")
        )
        self.ui.SY_spinBox_ZDSZ.setValue(
            self.settings.value("SY_spinBox_ZDSZ", 10, type=int)
        )
        # 加载新增的不透明度设置
        self.ui.SY_spinBox_ZDSZ_2.setValue(
            self.settings.value("SY_spinBox_ZDSZ_2", 15, type=int)  # 动态合合LOGO默认15%
        )
        self.ui.SY_spinBox_ZDSZ_3.setValue(
            self.settings.value("SY_spinBox_ZDSZ_3", 6, type=int)   # 全屏合合LOGO默认6%
        )
        self.ui.SY_spinBox_ZDSZ_4.setValue(
            self.settings.value("SY_spinBox_ZDSZ_4", 10, type=int)  # 预设全屏图片默认10%
        )

        # 加载AI标识复选框设置
        self.ui.checkBox_AI.setChecked(
            self.settings.value("checkBox_AI", False, type=bool)  # 默认不勾选
        )

        # 界面颜色设置
        dark_mode = self.settings.value("interface_dark_mode", False, type=bool)
        self.ui.checkBox.setChecked(dark_mode)
        self.is_dark_mode = dark_mode
        if dark_mode:
            self.apply_dark_theme()
        else:
            self.apply_light_theme()

    def _save_settings(self):
        # 抽帧去重设置
        self.settings.setValue("CZ_spinBox_low", self.ui.CZ_spinBox_low.value())
        self.settings.setValue("CZ_spinBox_max", self.ui.CZ_spinBox_max.value())

        # 转场功能设置
        self.settings.setValue("ZC_checkBox", self.ui.ZC_checkBox.isChecked())
        self.settings.setValue("ZC_comboBox", self.ui.ZC_comboBox.currentText())
        self.settings.setValue("ZC_doubleSpinBox", self.ui.ZC_doubleSpinBox.value())

        # 竖版素材自动洗设置
        self.settings.setValue("checkBox_AUTOSHU", self.ui.checkBox_AUTOSHU.isChecked())

        # 整体添加背景音乐设置
        self.settings.setValue("YY_lineEdit", self.ui.YY_lineEdit.text())
        self.settings.setValue("YY_spinBox_yyyl", self.ui.YY_spinBox_yyyl.value())
        self.settings.setValue("YY_spinBox_yspyl", self.ui.YY_spinBox_yspyl.value())

        # 批量重命名设置
        self.settings.setValue("CMM_spinBox", self.ui.CMM_spinBox.value())
        self.settings.setValue("CMM_lineEdit", self.ui.CMM_lineEdit.text())
        self.settings.setValue("CMM_lineEdit_2", self.ui.CMM_lineEdit_2.text())
        self.settings.setValue("spinBox", self.ui.spinBox.value())

        # 保存抽帧设置
        self.settings.setValue("DDCZ_spinBox_low_2", self.ui.DDCZ_spinBox_low_2.value())
        self.settings.setValue("DDCZ_spinBox_max_2", self.ui.DDCZ_spinBox_max_2.value())

        # 保存抽帧重命名设置
        self.settings.setValue("CMM_spinBox_2", self.ui.CMM_spinBox_2.value())
        self.settings.setValue("CMM_lineEdit_3", self.ui.CMM_lineEdit_3.text())
        self.settings.setValue("CMM_lineEdit_4", self.ui.CMM_lineEdit_4.text())
        self.settings.setValue("spinBox_2", self.ui.spinBox_2.value())
        self.settings.setValue("CMM_checkBox_5", self.ui.CMM_checkBox_5.isChecked())
        # CMM_checkBox_4 不保存设置

        # 保存tab_HJ相关设置
        self.settings.setValue("CZ_spinBox_low_2", self.ui.CZ_spinBox_low_2.value())
        self.settings.setValue("CZ_spinBox_max_2", self.ui.CZ_spinBox_max_2.value())
        self.settings.setValue("YY_lineEdit_2", self.ui.YY_lineEdit_2.text())
        self.settings.setValue("YY_spinBox_yspyl_2", self.ui.YY_spinBox_yspyl_2.value())
        self.settings.setValue("YY_spinBox_yyyl_2", self.ui.YY_spinBox_yyyl_2.value())
        self.settings.setValue("CMM_spinBox_3", self.ui.CMM_spinBox_3.value())
        self.settings.setValue("CMM_lineEdit_5", self.ui.CMM_lineEdit_5.text())
        self.settings.setValue("CMM_lineEdit_6", self.ui.CMM_lineEdit_6.text())
        self.settings.setValue("spinBox_3", self.ui.spinBox_3.value())

        # 保存LK_lineEdit_3设置
        self.settings.setValue("LK_lineEdit_3", self.ui.LK_lineEdit_3.text())

        # 保存YY_lineEdit_2设置
        self.settings.setValue("YY_lineEdit_2", self.ui.YY_lineEdit_2.text())

        # 保存洗素材设置
        self.settings.setValue("SXC_textEdit_W", self.ui.SXC_textEdit_W.toPlainText())
        self.settings.setValue("SXC_textEdit_H", self.ui.SXC_textEdit_H.toPlainText())
        self.settings.setValue("SXC_textEdit_BTL", self.ui.SXC_textEdit_BTL.toPlainText())

        # 保存水印工具设置
        self.settings.setValue("SY_lineEdit_6ZD", self.ui.SY_lineEdit_6ZD.text())
        self.settings.setValue("SY_spinBox_ZDSZ", self.ui.SY_spinBox_ZDSZ.value())
        self.settings.setValue("SY_spinBox_ZDSZ_2", self.ui.SY_spinBox_ZDSZ_2.value())  # 动态合合LOGO不透明度
        self.settings.setValue("SY_spinBox_ZDSZ_3", self.ui.SY_spinBox_ZDSZ_3.value())  # 全屏合合LOGO不透明度
        self.settings.setValue("SY_spinBox_ZDSZ_4", self.ui.SY_spinBox_ZDSZ_4.value())  # 预设全屏图片不透明度

        # 保存AI标识复选框设置
        self.settings.setValue("checkBox_AI", self.ui.checkBox_AI.isChecked())

        # 保存界面颜色设置
        self.settings.setValue("interface_dark_mode", self.ui.checkBox.isChecked())



    def _select_music_folder(self):
        dir_path = QFileDialog.getExistingDirectory(self, "选择音乐文件夹")
        if dir_path:
            self.ui.YY_lineEdit.setText(dir_path)
            self._save_settings()

    def add_main_files(self):
        files, _ = QFileDialog.getOpenFileNames(
            self,
            "选择前贴视频文件",
            "",
            "视频文件 (*.mp4 *.avi *.mkv *.mov *.flv *.wmv)",
        )
        if files:
            self._add_files(files, self.qt_model, self.main_files)

    def remove_selected_main_files(self):
        indexes = self.ui.QT_listView.selectedIndexes()
        if indexes:
            for idx in sorted(indexes, key=lambda x: x.row(), reverse=True):
                self.main_files.pop(idx.row())
            self.qt_model.setStringList([Path(f).name for f in self.main_files])

    def clear_main_files(self):
        self.main_files = []
        self.qt_model.setStringList([])

    def add_variant_files(self):
        files, _ = QFileDialog.getOpenFileNames(
            self,
            "选择后贴视频文件",
            "",
            "视频文件 (*.mp4 *.avi *.mkv *.mov *.flv *.wmv)",
        )
        if files:
            self._add_files(files, self.ht_model, self.variant_files)

    def remove_selected_variant_files(self):
        indexes = self.ui.HT_listView_2.selectedIndexes()
        if indexes:
            for idx in sorted(indexes, key=lambda x: x.row(), reverse=True):
                self.variant_files.pop(idx.row())
            self.ht_model.setStringList([Path(f).name for f in self.variant_files])

    def clear_variant_files(self):
        self.variant_files = []
        self.ht_model.setStringList([])

    def select_output_directory(self):
        dir_path = QFileDialog.getExistingDirectory(self, "选择输出目录")
        if dir_path:
            self.output_dir = dir_path
            self.ui.SC_lineEdit.setText(dir_path)

    def run_initial_check(self):
        # 验证前贴、后贴和输出目录
        if not self.main_files:
            QMessageBox.warning(self, "错误", "请先添加前贴素材！")
            return
        if not self.variant_files:
            QMessageBox.warning(self, "错误", "请先添加后贴素材！")
            return
        if not self.output_dir:
            QMessageBox.warning(self, "错误", "请先选择输出目录！")
            return

        # 准备文件路径
        self.mixer.main_files = [Path(f) for f in self.main_files]
        self.mixer.variant_files = [Path(f) for f in self.variant_files]
        self.mixer.output_dir = Path(self.output_dir)

        # 设置转场配置 - 每次初始化检查时都重新读取最新设置
        if self.ui.ZC_checkBox.isChecked():
            transition_type = self._parse_transition_type(self.ui.ZC_comboBox.currentText())
            transition_duration = self.ui.ZC_doubleSpinBox.value()
            self.mixer.transition_config = {
                'enabled': True,
                'type': transition_type,
                'duration': transition_duration
            }
            self.update_log(f"✅ 转场配置已启用: {transition_type}, 时长: {transition_duration}秒")
            self.update_log(f"🔧 转场复选框状态: {self.ui.ZC_checkBox.isChecked()}")
            self.update_log(f"🔧 转场类型: {self.ui.ZC_comboBox.currentText()}")
            self.update_log(f"🔧 转场时长: {self.ui.ZC_doubleSpinBox.value()}秒")
        else:
            self.mixer.transition_config = {'enabled': False}
            self.update_log("ℹ️ 转场功能未勾选，将使用简单拼接")

        # 执行初始化检查
        if not self.mixer.find_ffmpeg():
            QMessageBox.critical(self, "错误", "未找到 FFmpeg！")
            return

        # 检查是否启用自动洗素材功能
        auto_wash_enabled = self.ui.checkBox_AUTOSHU.isChecked()

        if not self.mixer.validate_files(self.mixer.main_files, "前贴"):
            return
        if not self.mixer.validate_files(self.mixer.variant_files, "后贴"):
            return

        if not auto_wash_enabled:
            # 只有在未启用自动洗素材时才检查格式一致性
            if not self.mixer.check_video_format(self.mixer.main_files, "前贴"):
                QMessageBox.warning(
                    self, "警告", "前贴素材存在格式不一致，可能导致合并失败！"
                )
            if not self.mixer.check_video_format(self.mixer.variant_files, "后贴"):
                QMessageBox.warning(
                    self, "警告", "后贴素材存在格式不一致，可能导致合并失败！"
                )
        else:
            # 启用自动洗素材时，格式不一致不是问题
            self.update_log("ℹ️ 已启用自动洗素材功能，将自动处理格式不一致问题")

        # 生成策略
        self.mixer.k = self.mixer.determine_optimal_k(
            len(self.mixer.main_files), len(self.mixer.variant_files)
        )
        self.mixer.strategies = self.mixer.generate_strategies()
        self._display_strategies()

        # 启用开始按钮
        self.ui.KS_pushButton.setEnabled(True)

    def _display_strategies(self):
        # 清空现有按钮
        for btn in self.strategy_buttons:
            btn.setParent(None)
        self.strategy_buttons = []

        # 添加新按钮
        for idx, (name, count) in enumerate(self.mixer.strategies, 1):
            btn = QRadioButton(f"{idx}. {name}（预计生成 {count} 个视频）")
            btn.clicked.connect(lambda _, s=name: self._select_strategy(s))
            self.strategy_layout.addWidget(btn)
            self.strategy_buttons.append(btn)

    def _select_strategy(self, strategy):
        self.selected_strategy = strategy
        self.update_log(f"已选择策略：{strategy}")

    def start_mix(self):
        if not self.selected_strategy:
            QMessageBox.warning(self, "错误", "请先选择策略！")
            return

        self.is_running = True
        self.ui.KS_pushButton.setEnabled(False)
        self.ui.TZ_pushButton.setEnabled(True)

        # 记录原始文件列表（只记录.mp4文件）
        self.original_files = {}
        self.modified_files = {}  # 初始化 modified_files 字典

        if self.output_dir:
            for f in os.listdir(self.output_dir):
                file_path = os.path.join(self.output_dir, f)
                if os.path.isfile(file_path) and os.path.splitext(f)[1].lower() == '.mp4':
                    self.original_files[file_path] = os.path.splitext(f)[1]

        # 修改所有原始视频文件扩展名为.MP，避免处理
        if self.original_files:
            self.update_log("开始标记输出目录中视频文件")
            for original_file, _ in self.original_files.items():
                modified_file = os.path.splitext(original_file)[0] + ".MP"
                try:
                    os.rename(original_file, modified_file)
                    self.modified_files[modified_file] = original_file  # 记录修改后的文件与原始文件的映射
                    self.update_log(f"✅ 重命名：{original_file} → {modified_file}")
                except Exception as e:
                    self.update_log(f"❌ 重命名失败：{original_file} → {modified_file}，错误：{str(e)[:50]}")

        self.mixer.selected_strategy = self.selected_strategy

        # 传递转场配置到mixer - 保持原始类型，在mixer中解析随机
        if self.ui.ZC_checkBox.isChecked():
            transition_type = self.ui.ZC_comboBox.currentText()
            transition_duration = self.ui.ZC_doubleSpinBox.value()

            self.mixer.transition_config = {
                'enabled': True,
                'type': transition_type,  # 保持原始类型，不在这里解析随机
                'duration': transition_duration
            }
            self.update_log(f"✅ 转场配置已传递到混剪器: {transition_type}, {transition_duration}秒")
        else:
            self.mixer.transition_config = {'enabled': False}
            self.update_log("ℹ️ 转场功能未启用")

        # 检查是否启用竖版素材自动洗功能
        if self.ui.checkBox_AUTOSHU.isChecked():
            self.update_log("🔄 启用竖版素材自动洗功能，开始预处理...")
            self.auto_wash_thread = AutoWashMixingThread(self.mixer, self)
            self.auto_wash_thread.log_signal.connect(self.update_log)
            self.auto_wash_thread.progress_signal.connect(self.update_progress)
            self.auto_wash_thread.finished.connect(self.on_mixing_finished)
            self.auto_wash_thread.start()
        else:
            self.mixing_thread = MixingThread(self.mixer)
            self.mixing_thread.log_signal.connect(self.update_log)
            self.mixing_thread.progress_signal.connect(self.update_progress)
            self.mixing_thread.finished.connect(self.on_mixing_finished)
            self.mixing_thread.start()

    def stop_mix(self):
        """停止混剪和所有扩展功能"""
        self.mixer.stop()
        self.is_running = False  # 设置全局停止标志

        # 停止混剪线程
        if hasattr(self, 'mixing_thread') and self.mixing_thread.isRunning():
            self.mixing_thread.terminate()

        # 停止自动洗混剪线程
        if hasattr(self, 'auto_wash_thread') and self.auto_wash_thread.isRunning():
            self.auto_wash_thread.terminate()

        # 重置按钮状态
        self.ui.KS_pushButton.setEnabled(True)
        self.ui.TZ_pushButton.setEnabled(False)
        self.ui.progressBar.setValue(0)

        QMessageBox.information(self, "提示", "混剪任务已停止")

    def update_log(self, message):
        self.ui.CL_plainTextEdit.appendPlainText(message)

    def add_files_from_drag(self, files, list_type):
        """处理拖放添加的文件"""
        print("【调试】拖放的文件完整路径列表：", files)  # 添加调试打印
        video_files = [f for f in files if self._is_video_file(f)]
        print("【调试】筛选后的视频文件路径列表：", video_files)  # 添加调试打印
        if not video_files:
            QMessageBox.warning(self, "提示", "请拖放视频文件！")
            return

        if list_type == "main":
            self._add_files(video_files, self.qt_model, self.main_files)
        elif list_type == "variant":
            self._add_files(video_files, self.ht_model, self.variant_files)
        elif list_type == "frame":
            self._add_frame_files(video_files)
        elif list_type == "hj":
            self._add_hj_files(video_files)
        elif list_type == "lk":
            self._add_lk_files(video_files)
        elif list_type == "xsc":
            self._add_xsc_files(video_files)
        elif list_type == "sy":
            self._add_sy_files(video_files)

    def _add_files(self, files, model, target_list):
        """通用添加文件方法，同步更新模型和目标列表"""
        current_files = [f for f in model.stringList()]  # 转换为普通列表
        new_files = []

        for file in files:
            file_name = Path(file).name
            if file_name not in current_files:
                new_files.append(file)
                current_files.append(file_name)

        if new_files:
            target_list.extend(new_files)
            model.setStringList(current_files)
        #   QMessageBox.information(self, "成功", f"已添加 {len(new_files)} 个文件")
        # else:
        #     QMessageBox.information(
        #         self, "提示", "没有新文件被添加（已存在或非视频文件）"
        #     )

    def _is_video_file(self, file_path):
        video_extensions = [".mp4", ".avi", ".mkv", ".mov", ".flv", ".wmv"]
        return Path(file_path).suffix.lower() in video_extensions

    def update_progress(self, current, total):
        progress = int((current / total) * 100)
        self.ui.progressBar.setValue(progress)

    def batch_rename(self, files):
        # 这里添加批量重命名的具体逻辑
        processed_files = []
        for file in files:
            # 示例：简单地在文件名前添加 "renamed_"
            base_name = os.path.basename(file)
            new_name = f"renamed_{base_name}"
            new_path = os.path.join(os.path.dirname(file), new_name)
            try:
                os.rename(file, new_path)
                self.update_log(f"✅ 重命名：{file} → {new_path}")
                processed_files.append(new_path)
            except Exception as e:
                self.update_log(f"❌ 重命名失败：{str(e)[:50]}")
                processed_files.append(file)
        return processed_files

    def on_mixing_finished(self):
        # 不要在这里设置 is_running = False，让扩展功能继续执行
        # self.is_running = False  # 注释掉这行

        # 不要在这里重置按钮状态，等所有功能完成后再重置
        # self.ui.KS_pushButton.setEnabled(True)
        # self.ui.TZ_pushButton.setEnabled(False)

        # 初始化文件跟踪字典
        self.file_tracker = {}

        # 获取混剪后新生成的文件列表（排除原始文件）
        mixed_files = []
        if self.output_dir:
            all_files = [
                os.path.join(self.output_dir, f)
                for f in os.listdir(self.output_dir)
                if os.path.isfile(os.path.join(self.output_dir, f))
            ]

            # 区分原始文件和混剪文件
            for file in all_files:
                # 检查是否是原始文件的修改版本
                is_original_file = False
                if hasattr(self, 'modified_files') and self.modified_files:
                    is_original_file = file in self.modified_files.values()

                if is_original_file:  # 是原始文件的修改版本
                    self.file_tracker[file] = {
                        'type': 'original',
                        'status': 'inactive',  # 原始文件暂时标记为不活跃
                        'history': [file]
                    }
                elif os.path.splitext(file)[1].lower() == '.mp4':  # 是新生成的混剪文件
                    self.file_tracker[file] = {
                        'type': 'mixed',
                        'status': 'active',
                        'history': [file]
                    }
                    mixed_files.append(file)

        # 计算扩展功能的数量
        extension_count = sum(
            [
                self.ui.ZC_checkBox.isChecked(),
                self.ui.CZ_checkBox.isChecked(),
                self.ui.YY_checkBox_2.isChecked(),
                self.ui.CMM_checkBox_2.isChecked(),
            ]
        )
        # 计算进度条的分配 - 统一进度条显示
        total_steps = 1 + extension_count  # 混剪 + 扩展功能
        step_progress = 100 / total_steps if total_steps > 0 else 100
        current_step = 1  # 混剪完成算第1步

        # 设置混剪完成后的进度
        self.ui.progressBar.setValue(int(current_step * step_progress))

        # 执行扩展功能（只处理混剪生成的文件）
        # 按照指定顺序执行：转场 -> 抽帧 -> 背景音乐 -> 批量重命名
        if self.ui.ZC_checkBox.isChecked() and mixed_files and self.is_running:
            self.update_log("开始执行转场扩展功能")
            mixed_files = self.apply_transitions(mixed_files)
            self._update_file_tracker(mixed_files)  # 更新文件跟踪器
            current_step += 1
            self.ui.progressBar.setValue(int(current_step * step_progress))

        if self.ui.CZ_checkBox.isChecked() and mixed_files and self.is_running:
            self.update_log("开始执行抽帧去重扩展功能")
            mixed_files = self.process_frame_removal(mixed_files)
            self._update_file_tracker(mixed_files)  # 更新文件跟踪器
            current_step += 1
            self.ui.progressBar.setValue(int(current_step * step_progress))

        if self.ui.YY_checkBox_2.isChecked() and mixed_files and self.is_running:
            self.update_log("开始执行添加背景音乐扩展功能")
            mixed_files = self.add_background_music(mixed_files)
            self._update_file_tracker(mixed_files)  # 更新文件跟踪器
            current_step += 1
            self.ui.progressBar.setValue(int(current_step * step_progress))

        if self.ui.CMM_checkBox_2.isChecked() and mixed_files and self.is_running:
            self.update_log("开始执行批量重命名扩展功能")
            mixed_files = self.batch_rename(mixed_files)
            self._update_file_tracker(mixed_files)  # 更新文件跟踪器
            current_step += 1
            self.ui.progressBar.setValue(int(current_step * step_progress))

        # 恢复原始文件扩展名
        if hasattr(self, 'modified_files') and self.modified_files:
            for modified_file, original_file in self.modified_files.items():
                if os.path.exists(modified_file):
                    try:
                        os.rename(modified_file, original_file)
                        self.file_tracker[original_file] = {
                            'type': 'original',
                            'status': 'active',
                            'history': [modified_file, original_file]
                        }
                        self.update_log(f"✅ 恢复扩展名：{modified_file} → {original_file}")
                    except Exception as e:
                        self.update_log(f"❌ 恢复扩展名失败：{modified_file} → {original_file}，错误：{str(e)[:50]}")

        # 确定需要保留的最终文件
        files_to_keep = []

        # 添加原始文件
        for file, info in self.file_tracker.items():
            if info['type'] == 'original' and info['status'] == 'active':
                files_to_keep.append(file)

        # 添加最新处理的混剪文件
        for file, info in self.file_tracker.items():
            if info['type'] == 'mixed' and info['status'] == 'active':
                latest_version = info['history'][-1]
                if latest_version not in files_to_keep:
                    files_to_keep.append(latest_version)

        # 删除中间文件
        all_files = [os.path.join(self.output_dir, f) for f in os.listdir(self.output_dir) if os.path.isfile(os.path.join(self.output_dir, f))]

        # 添加调试信息
        self.update_log(f"🔍 文件清理调试信息:")
        self.update_log(f"   输出目录中的所有文件: {len(all_files)} 个")
        self.update_log(f"   需要保留的文件: {len(files_to_keep)} 个")

        # 定义视频文件扩展名
        video_extensions = [".mp4", ".avi", ".mkv", ".mov", ".flv", ".wmv", ".MP4", ".AVI", ".MKV", ".MOV", ".FLV", ".WMV"]

        # 定义中间文件模式
        temp_patterns = [
            "temp_segment_",  # 临时片段文件
            "concat_list.txt",  # concat列表文件
            "temp_",  # 其他临时文件
        ]

        deleted_count = 0
        for file in all_files:
            if file not in files_to_keep:
                file_name = os.path.basename(file)
                file_ext = os.path.splitext(file_name)[1]

                # 只删除视频文件或明确的临时文件，不删除其他类型文件（如.zip等）
                is_video_file = file_ext in video_extensions
                is_temp_file = any(pattern in file_name for pattern in temp_patterns)

                if is_video_file or is_temp_file:
                    try:
                        os.remove(file)
                        self.update_log(f"✅ 删除中间文件：{os.path.basename(file)}")
                        deleted_count += 1
                    except Exception as e:
                        self.update_log(f"❌ 删除中间文件 {os.path.basename(file)} 失败：{str(e)[:50]}")
                else:
                    self.update_log(f"⏩ 跳过非视频文件：{os.path.basename(file)}")

        if deleted_count == 0:
            self.update_log("ℹ️ 没有中间文件需要删除")
        else:
            self.update_log(f"🧹 共删除 {deleted_count} 个中间文件")

        # 所有任务完成后重置状态和显示完成提示
        self.is_running = False
        self.ui.KS_pushButton.setEnabled(True)
        self.ui.TZ_pushButton.setEnabled(False)
        self.ui.progressBar.setValue(100)

        QMessageBox.information(self, "成功", "所有任务完成！")


    def _update_file_tracker(self, processed_files):
        """更新文件跟踪器，确保正确记录文件处理历史"""
        for new_file in processed_files:
            # 查找该文件的原始版本
            found = False
            for file, info in self.file_tracker.items():
                if new_file in info['history']:
                    found = True
                    break

            if not found:
                # 查找是否是已有文件的更新版本
                base_name = os.path.basename(new_file)
                # 使用正则表达式匹配可能的文件名模式
                for file, info in self.file_tracker.items():
                    if info['type'] == 'mixed':
                        old_base = os.path.basename(file)
                        # 检查是否是同一文件的不同版本
                        if re.search(r'merged_\d+_\d+', old_base) and re.search(r'merged_\d+_\d+', base_name):
                            # 属于同一混剪文件的不同版本
                            info['history'].append(new_file)
                            # 更新文件跟踪器中的条目
                            self.file_tracker.pop(file)
                            self.file_tracker[new_file] = info
                            found = True
                            break
                        elif old_base in base_name or base_name in old_base:
                            # 基于名称相似性判断
                            info['history'].append(new_file)
                            self.file_tracker.pop(file)
                            self.file_tracker[new_file] = info
                            found = True
                            break

            if not found:
                # 全新的文件（可能是用户手动添加的）
                self.file_tracker[new_file] = {
                    'type': 'mixed',
                    'status': 'active',
                    'history': [new_file]
                }

    # 整体添加背景音乐功能
    def add_background_music(self, files):
        processed_files = []

        for file in files:
            try:
                base_name = os.path.splitext(os.path.basename(file))[0]
                new_file_name = f"{base_name}_with_music.mp4"
                new_file_path = os.path.join(os.path.dirname(file), new_file_name)

                self.update_log(f"开始处理文件：{file}")

                # 获取音乐文件夹
                music_folder = self.ui.YY_lineEdit.text()
                if not music_folder or not os.path.exists(music_folder):
                    self.update_log("❌ 音乐文件夹不存在或未选择")
                    processed_files.append(file)
                    continue

                # 获取音乐文件列表 - 支持更多音频格式
                music_files = [
                    f for f in os.listdir(music_folder)
                    if f.lower().endswith(('.mp3', '.wav', '.aac', '.m4a', '.flac', '.ogg')) and os.path.isfile(os.path.join(music_folder, f))
                ]

                if not music_files:
                    self.update_log("❌ 音乐文件夹中无可用音乐文件")
                    self.update_log(f"   检查的文件夹：{music_folder}")
                    self.update_log(f"   支持的格式：mp3, wav, aac, m4a, flac, ogg")
                    processed_files.append(file)
                    continue

                # 随机选择音乐
                selected_music = random.choice(music_files)
                music_path = os.path.join(music_folder, selected_music)
                self.update_log(f"🎵 选择音乐：{selected_music}")

                # 获取视频时长
                duration_cmd = [
                    "ffprobe",
                    "-v", "error",
                    "-show_entries", "format=duration",
                    "-of", "default=noprint_wrappers=1:nokey=1",
                    file
                ]

                try:
                    result = subprocess.run(
                        duration_cmd,
                        capture_output=True,
                        text=True,
                        check=True,
                        encoding='utf-8',
                        creationflags=CREATE_NO_WINDOW  #隐藏黑色弹窗
                    )
                    video_duration = float(result.stdout.strip())
                except Exception as e:
                    self.update_log(f"❌ 获取视频时长失败：{str(e)[:50]}")
                    processed_files.append(file)
                    continue

                # 设置音量
                music_volume = self.ui.YY_spinBox_yyyl.value() / 10.0  # 转换为小数
                video_volume = self.ui.YY_spinBox_yspyl.value() / 10.0  # 转换为小数

                # 检查音量设置
                if music_volume <= 0:
                    music_volume = 0.6  # 默认音乐音量
                    self.update_log(f"⚠️ 音乐音量为0，使用默认值：{music_volume}")
                if video_volume <= 0:
                    video_volume = 0.8  # 默认视频音量
                    self.update_log(f"⚠️ 视频音量为0，使用默认值：{video_volume}")

                self.update_log(f"🔊 音量设置 - 视频：{video_volume}, 音乐：{music_volume}")

                # 检查视频是否有音频流
                has_video_audio = self._check_audio_stream(file)

                # 构建FFmpeg命令
                if has_video_audio:
                    # 视频有音频，混合原音频和背景音乐
                    ffmpeg_cmd = [
                        "ffmpeg",
                        "-i", file,
                        "-i", music_path,
                        "-filter_complex",
                        f"[0:a]volume={video_volume}[v0];[1:a]aloop=loop=-1:size=2e+09,volume={music_volume}[v1];[v0][v1]amix=inputs=2:duration=first:normalize=0[aout]",
                        "-map", "0:v:0",
                        "-map", "[aout]",
                        "-c:v", "copy",
                        "-c:a", "aac",
                        "-b:a", "256k",
                        "-ar", "48000",
                        "-shortest",
                        "-y",
                        new_file_path
                    ]
                    self.update_log(f"🎵 混合原音频和背景音乐")
                else:
                    # 视频没有音频，只添加背景音乐
                    ffmpeg_cmd = [
                        "ffmpeg",
                        "-i", file,
                        "-i", music_path,
                        "-filter_complex",
                        f"[1:a]aloop=loop=-1:size=2e+09,volume={music_volume}[aout]",
                        "-map", "0:v:0",
                        "-map", "[aout]",
                        "-c:v", "copy",
                        "-c:a", "aac",
                        "-b:a", "256k",
                        "-ar", "48000",
                        "-shortest",
                        "-y",
                        new_file_path
                    ]
                    self.update_log(f"🎵 为无音频视频添加背景音乐")

                # 执行命令
                result = subprocess.run(
                    ffmpeg_cmd,
                    capture_output=True,
                    text=True,
                    encoding='utf-8',
                    creationflags=CREATE_NO_WINDOW  #隐藏黑色弹窗
                )

                if result.returncode != 0:
                    self.update_log(f"❌ 失败：{result.stderr[:100]}")
                    processed_files.append(file)
                else:
                    self.update_log(f"✅ 完成：{file} → {new_file_path}")
                    processed_files.append(new_file_path)

            except Exception as e:
                self.update_log(f"❌ 失败：{str(e)[:100]}")
                processed_files.append(file)

        return processed_files


    # 抽帧去重功能
    def process_frame_removal(self, files):
        print("进入 process_frame_removal 方法")
        DELETE_FRAMES_MIN = self.ui.CZ_spinBox_low.value()
        DELETE_FRAMES_MAX = self.ui.CZ_spinBox_max.value()
        processed_files = []
        total_files = len(files)
        for i, file in enumerate(files):
            input_path = file
            safe_name = sanitize_filename(os.path.basename(file))
            # 确保输出文件名包含 .mp4 扩展名
            output_name = f"{os.path.splitext(safe_name)[0]}_processed.mp4"
            output_path = os.path.join(self.output_dir, output_name)

            if os.path.exists(output_path):
                continue

            meta = get_video_metadata(input_path)
            if not meta:
                self.update_log(f"❌ {file} 元数据解析失败，详细原因请检查控制台输出")
                continue

            # 检查 nb_frames 键是否存在
            if 'nb_frames' not in meta.get('streams', [{}])[0]:
                self.update_log(f"❌ {file} 无法获取帧数信息，元数据内容：{meta}")
                continue

            total_frames = int(meta['streams'][0]['nb_frames'])
            delete_frames = random.randint(DELETE_FRAMES_MIN, DELETE_FRAMES_MAX)
            if total_frames < delete_frames:
                self.update_log(
                    f"❌ {file} 帧数不足（{total_frames} < {delete_frames}）"
                )
                continue

            frame_ids = sorted(random.sample(range(total_frames), delete_frames))
            frame_duration = float(meta["format"]["duration"]) / total_frames
            time_expr = [
                f"between(t,{f*frame_duration:.6f},{(f+1)*frame_duration:.6f})"
                for f in frame_ids
            ]

            # 构建基础FFmpeg命令
            base_cmd = [
                "ffmpeg",
                "-i", input_path,
                "-hide_banner",
                "-vf",
                f"select='not({'+'.join(time_expr)})',setpts=N/FRAME_RATE/TB",
                "-af",
                f"aselect='not({'+'.join(time_expr)})',asetpts=N/SR/TB",
                "-c:v", "placeholder",  # 将被GPU函数替换
                "-b:v",
                VIDEO_BITRATE,
                "-maxrate",
                "18000k",
                "-bufsize",
                "36000k",
                "-c:a",
                "aac",
                "-b:a",
                "320k",
                "-y",
                output_path
            ]

            # 使用统一的GPU加速函数
            cmd, using_gpu = build_gpu_ffmpeg_cmd(base_cmd)
            if using_gpu:
                self.update_log("🚀 使用GPU硬件加速处理")
            else:
                self.update_log("🔄 使用CPU处理")

            try:
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    encoding='utf-8',
                    timeout=1800,
                    creationflags=CREATE_NO_WINDOW  #隐藏黑色弹窗
                )
                if result.returncode != 0:
                    self.update_log(f"❌ 失败：{result.stderr}，执行的命令：{' '.join(cmd)}")
                else:
                    output_meta = get_video_metadata(output_path)
                    output_frames = int(output_meta["streams"][0]["nb_frames"]) if output_meta and 'nb_frames' in output_meta.get('streams', [{}])[0] else 0
                    self.update_log(
                        f"✅ 完成：{file}（原帧：{total_frames} → 新帧：{output_frames} 丨 抽帧：{total_frames - output_frames}）"
                    )
                    processed_files.append(output_path)
            except subprocess.TimeoutExpired:
                self.update_log(f"❌ 失败：执行超时，命令：{' '.join(cmd)}")
            except Exception as e:
                self.update_log(f"❌ 失败：{str(e)}，执行的命令：{' '.join(cmd)}")

            # 不在这里更新进度条，由外部统一管理

        return processed_files



    # 转场功能
    def apply_transitions(self, files):
        """为视频文件添加转场特效"""
        if not self.ui.ZC_checkBox.isChecked():
            return files

        # tab_QT的转场功能应该在混剪过程中应用，而不是后处理
        # 如果用户勾选了转场扩展功能，说明转场配置没有正确传递到混剪过程
        self.update_log("ℹ️ tab_QT的转场功能应该在混剪过程中应用")

        # 检查转场是否已在混剪中应用
        if hasattr(self.mixer, 'transition_config') and self.mixer.transition_config.get('enabled', False):
            self.update_log("✅ 转场功能已在混剪过程中应用，跳过后处理转场")
            return files
        else:
            self.update_log("⚠️ 转场配置未正确传递到混剪过程，请检查转场设置")
            self.update_log("💡 提示：tab_QT的转场应该在混剪时应用，而不是后处理")
            return files

    def _parse_transition_type(self, transition_text):
        """解析转场类型文本，返回FFmpeg转场效果名称"""
        # 完整的转场映射表，包含所有42个选项
        transition_map = {
            "【【全随机】】": "random_all",
            "【【柔 · 随机】】": "random_soft",
            "【【硬 · 随机】】": "random_hard",
            "【柔】【叠化】fade": "fade",
            "【向左擦除】wipeleft": "wipeleft",
            "【向右擦除】wiperight": "wiperight",
            "【向上擦除】wipeup": "wipeup",
            "【向下擦除】wipedown": "wipedown",
            "【柔】【向左擦除】smoothleft": "smoothleft",
            "【柔】【向右擦除】smoothright": "smoothright",
            "【柔】【向上擦除】smoothup": "smoothup",
            "【柔】【向下擦除】smoothdown": "smoothdown",
            "【向左滑动】slideleft": "slideleft",
            "【向右滑动】slideright": "slideright",
            "【向上滑动】slideup": "slideup",
            "【向下滑动】slidedown": "slidedown",
            "【柔】【圆形展开】circleopen": "circleopen",
            "【柔】【圆形闭合】circleclose": "circleclose",
            "【柔】【垂直展开】vertopen": "vertopen",
            "【柔】【垂直闭合】vertclose": "vertclose",
            "【柔】【水平展开】horzopen": "horzopen",
            "【柔】【水平闭合】horzclose": "horzclose",
            "【柔】【景深转场】distance": "distance",
            "【时钟擦除】radial": "radial",
            "【像素模糊】pixelize": "pixelize",
            "【放大转场】zoomin": "zoomin",
            "【柔】【向左上擦除】diagtl": "diagtl",
            "【柔】【向右上擦除】diagtr": "diagtr",
            "【柔】【向左下擦除】diagbl": "diagbl",
            "【柔】【向右下擦除】diagbr": "diagbr",
            "【向左上擦除】wipetl": "wipetl",
            "【向右上擦除】wipetr": "wipetr",
            "【向左下擦除】wipebl": "wipebl",
            "【向右下擦除】wipebr": "wipebr",
            "【向左百叶窗】hlslice": "hlslice",
            "【向右百叶窗】hrslice": "hrslice",
            "【向上百叶窗】vuslice": "vuslice",
            "【向下百叶窗】vdslice": "vdslice",
            "【向左滑刺】hlwind": "hlwind",
            "【向右滑刺】hrwind": "hrwind",
            "【向上滑刺】vuwind": "vuwind",
            "【向下滑刺】vdwind": "vdwind"
        }

        # 对于随机转场，返回标识符而不是具体转场效果
        # 这样在_merge_with_transitions中可以正确处理每段都随机的逻辑

        return transition_map.get(transition_text, "fade")

    def _merge_videos_with_transition(self, videos, output_path, transition, duration):
        """使用FFmpeg合并视频并添加转场效果"""
        if len(videos) < 2:
            return False

        try:
            # 处理随机转场
            if transition.startswith("random"):
                if transition == "random_all":
                    # 全随机：从所有转场中选择 - 包含用户要求的42个特效
                    all_transitions = ["fade", "smoothleft", "smoothright", "smoothup", "smoothdown",
                                     "circleopen", "circleclose", "vertopen", "vertclose", "horzopen", "horzclose",
                                     "distance", "diagtl", "diagtr", "diagbl", "diagbr", "wipeleft", "wiperight",
                                     "wipeup", "wipedown", "slideleft", "slideright", "slideup", "slidedown",
                                     "radial", "pixelize", "zoomin", "wipetl", "wipetr", "wipebl", "wipebr",
                                     "hlslice", "hrslice", "vuslice", "vdslice", "hlwind", "hrwind", "vuwind", "vdwind"]
                    transition = random.choice(all_transitions)
                elif transition == "random_soft":
                    # 柔随机：只从柔和转场中选择 - 16个柔和特效
                    soft_transitions = ["fade", "smoothleft", "smoothright", "smoothup", "smoothdown",
                                      "circleopen", "circleclose", "vertopen", "vertclose",
                                      "horzopen", "horzclose", "distance", "diagtl", "diagtr", "diagbl", "diagbr"]
                    transition = random.choice(soft_transitions)
                elif transition == "random_hard":
                    # 硬随机：从硬切转场中选择 - 20个硬切特效
                    hard_transitions = ["wipeleft", "wiperight", "wipeup", "wipedown", "slideleft", "slideright",
                                      "slideup", "slidedown", "radial", "pixelize", "zoomin", "wipetl", "wipetr",
                                      "wipebl", "wipebr", "hlslice", "hrslice", "vuslice", "vdslice",
                                      "hlwind", "hrwind", "vuwind", "vdwind"]
                    transition = random.choice(hard_transitions)

            # 构建FFmpeg命令
            inputs = []
            for video in videos:
                inputs.extend(["-i", video])

            # 构建滤镜复合体
            filter_complex = []

            # 获取第一个视频的分辨率作为目标分辨率
            try:
                from utils import get_video_metadata
                first_video_meta = get_video_metadata(videos[0])
                if first_video_meta and 'streams' in first_video_meta:
                    video_stream = first_video_meta['streams'][0]
                    target_width = video_stream.get('width', 1920)
                    target_height = video_stream.get('height', 1080)
                else:
                    target_width, target_height = 1920, 1080
            except:
                target_width, target_height = 1920, 1080

            self.update_log(f"使用目标分辨率: {target_width}x{target_height}")

            # 为每个视频添加预处理 - 使用素材的原始分辨率
            for i in range(len(videos)):
                filter_complex.append(f"[{i}:v]scale={target_width}:{target_height}:force_original_aspect_ratio=decrease,pad={target_width}:{target_height}:(ow-iw)/2:(oh-ih)/2[v{i}]")

            # 添加转场效果 - 修复引号和offset计算
            current_output = "v0"
            cumulative_offset = 0.0

            for i in range(1, len(videos)):
                # 获取前一个视频的时长来计算正确的offset
                try:
                    from utils import get_video_duration
                    prev_duration = get_video_duration(videos[i-1])
                    if prev_duration > 0:
                        # 确保转场时长不超过前一个视频时长的一半
                        safe_duration = min(duration, prev_duration * 0.5)
                        # 计算转场开始的offset（前一个视频结束前safe_duration秒开始转场）
                        offset = cumulative_offset + prev_duration - safe_duration
                        cumulative_offset += prev_duration - safe_duration
                    else:
                        safe_duration = duration
                        offset = cumulative_offset
                        cumulative_offset += safe_duration
                except:
                    # 如果无法获取时长，使用默认值
                    safe_duration = duration
                    offset = cumulative_offset
                    cumulative_offset += safe_duration

                next_output = f"v{i}" if i == len(videos) - 1 else f"trans{i}"
                # 使用单引号包围转场名称，修复offset计算
                filter_complex.append(f"[{current_output}][v{i}]xfade=transition='{transition}':duration={safe_duration:.3f}:offset={offset:.3f}[{next_output}]")
                current_output = next_output

            # 音频处理 - 修复音频呲呲啦啦声音
            audio_inputs = []
            for i in range(len(videos)):
                # 为每个音频流添加音量控制，避免音频失真
                audio_inputs.append(f"[{i}:a]volume=0.8[a{i}]")

            # 使用修复后的音频混合参数
            audio_mix_inputs = "".join([f"[a{i}]" for i in range(len(videos))])
            filter_complex.extend(audio_inputs)
            filter_complex.append(f"{audio_mix_inputs}amix=inputs={len(videos)}:duration=first:normalize=0[aout]")

            filter_str = ";".join(filter_complex)

            cmd = [
                "ffmpeg",
                *inputs,
                "-filter_complex", filter_str,
                "-map", f"[{current_output}]",
                "-map", "[aout]",
                "-c:v", "libx264",
                "-preset", "slow",
                "-crf", "15",
                "-c:a", "aac",
                "-y",
                output_path
            ]

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding='utf-8',
                timeout=3600,
                creationflags=CREATE_NO_WINDOW
            )

            if result.returncode == 0:
                return True
            else:
                self.update_log(f"❌ FFmpeg错误：{result.stderr[:200]}")
                return False

        except Exception as e:
            self.update_log(f"❌ 转场处理异常：{str(e)[:100]}")
            return False

    # 批量重命名功能
    def batch_rename(self, files):
        start_value = self.ui.CMM_spinBox.value()
        prefix = self.ui.CMM_lineEdit.text()
        suffix = self.ui.CMM_lineEdit_2.text()
        fill_zeros = self.ui.spinBox.value()
        keep_extension = self.ui.CMM_checkBox_3.isChecked()

        # 需要跳过的临时扩展名列表
        temp_extensions = ['.mp']

        total_files = len(files)  # 定义文件总数
        processed_files = []
        renamed_conflicts = []  # 记录重命名冲突的文件

        for i, file in enumerate(files, start=start_value):
            # 获取文件完整路径的扩展名
            file_ext = os.path.splitext(file)[1]

            # 检查文件扩展名是否是需要跳过的临时扩展名
            if file_ext.lower() in temp_extensions:
                self.update_log(f"⏩ 跳过临时扩展名文件：{file}")
                processed_files.append(file)
                continue

            # 获取文件所在目录
            file_dir = os.path.dirname(file)

            # 构建新文件名，保留原始扩展名或使用用户指定的
            base_name = os.path.basename(file)
            original_ext = os.path.splitext(base_name)[1] if keep_extension else ""
            new_name = f"{prefix}{str(i).zfill(fill_zeros)}{suffix}{original_ext}"

            # 使用原始文件的目录构建新路径
            new_path = os.path.join(file_dir, new_name)

            # 检查文件名冲突（包括被临时重命名为.MP的原始文件）
            conflict_detected = False

            # 检查当前是否存在同名文件
            if os.path.exists(new_path) and new_path != file:
                conflict_detected = True

            # 检查是否与被临时重命名的原始文件冲突
            if not conflict_detected and hasattr(self, 'modified_files') and self.modified_files:
                # 构建对应的.MP文件路径
                mp_path = os.path.splitext(new_path)[0] + ".MP"
                if mp_path in self.modified_files:
                    conflict_detected = True
                    self.update_log(f"⚠️ 检测到与原始文件冲突：{new_name}（原始文件已临时重命名为.MP）")

            if conflict_detected:
                # 如果存在冲突，添加_B后缀
                name_without_ext = os.path.splitext(new_name)[0]
                ext = os.path.splitext(new_name)[1]
                conflict_name = f"{name_without_ext}_B{ext}"
                conflict_path = os.path.join(file_dir, conflict_name)

                # 记录冲突信息
                renamed_conflicts.append({
                    'original_intended': new_name,
                    'actual_name': conflict_name
                })

                new_name = conflict_name
                new_path = conflict_path
                self.update_log(f"⚠️ 发现重名文件，新文件已改名为：{new_name}")

            try:
                os.rename(file, new_path)
                self.update_log(f"✅ 重命名：{file} → {new_path}")
                processed_files.append(new_path)
            except Exception as e:
                self.update_log(f"❌ 重命名失败：{str(e)[:50]}")
                processed_files.append(file)  # 失败时保留原文件

            # 更新进度条
            current_progress = int((i - start_value + 1) / total_files * 100)
            self.ui.progressBar.setValue(current_progress)

        # 如果有重命名冲突，显示弹窗提示
        if renamed_conflicts:
            conflict_message = "发现重复命名，以下文件已自动调整：\n\n"
            for conflict in renamed_conflicts:
                conflict_message += f"原计划名称：{conflict['original_intended']}\n"
                conflict_message += f"实际名称：{conflict['actual_name']}\n\n"

            QMessageBox.information(self, "重命名冲突提示", conflict_message)

        return processed_files

    def add_frame_files(self):
        """添加要处理的视频文件"""
        files, _ = QFileDialog.getOpenFileNames(
            self,
            "选择视频文件",
            "",
            "视频文件 (*.mp4 *.avi *.mkv *.mov *.flv *.wmv)",
        )
        if files:
            self._add_frame_files(files)

    def _add_frame_files(self, files):
        """添加文件到抽帧列表"""
        current_files = [f for f in self.frame_model.stringList()]
        new_files = []

        for file in files:
            file_name = Path(file).name
            if file_name not in current_files:
                new_files.append(file)
                current_files.append(file_name)

        if new_files:
            self.frame_files.extend(new_files)
            self.frame_model.setStringList(current_files)

    def remove_selected_frame_files(self):
        """移除选中的文件"""
        indexes = self.ui.DDCZ_listView_3.selectedIndexes()
        if indexes:
            for idx in sorted(indexes, key=lambda x: x.row(), reverse=True):
                self.frame_files.pop(idx.row())
            self.frame_model.setStringList([Path(f).name for f in self.frame_files])

    def clear_frame_files(self):
        """清空文件列表"""
        self.frame_files = []
        self.frame_model.setStringList([])

    def select_frame_output_directory(self):
        """选择输出目录"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择输出目录")
        if dir_path:
            self.ui.DDCZ_lineEdit_3.setText(dir_path)

    def on_frame_radio_toggled(self):
        """处理单选按钮状态改变"""
        replace_mode = self.ui.DDCZ_radioButton_TH.isChecked()
        # 禁用/启用相关控件
        self.ui.DDCZ_lineEdit_3.setEnabled(not replace_mode)
        self.ui.DDCZ_where_3.setEnabled(not replace_mode)
        self.ui.DDCZ_groupBox_KZ.setEnabled(not replace_mode)

        # 当选择替换原素材模式时，强制取消勾选 CMM_checkBox_4
        if replace_mode:
            self.ui.CMM_checkBox_4.setChecked(False)

    def start_frame_removal(self):
        """开始抽帧处理"""
        if not self.frame_files:
            QMessageBox.warning(self, "错误", "请先添加要处理的视频文件！")
            return

        replace_mode = self.ui.DDCZ_radioButton_TH.isChecked()
        if not replace_mode and not self.ui.DDCZ_lineEdit_3.text():
            QMessageBox.warning(self, "错误", "请选择输出目录！")
            return

        # 检查输出目录中的文件
        if not replace_mode:
            output_dir = self.ui.DDCZ_lineEdit_3.text()
            files_to_rename = []  # 存储需要重命名的文件信息
            to_process_files = set()  # 存储要处理的文件名（不含路径）
            original_files = []  # 存储在输出目录中找到的原文件
            other_files = []  # 存储其他非处理文件

            # 收集要处理的文件名
            for file in self.frame_files:
                to_process_files.add(os.path.basename(file))

            # 检查输出目录中的所有视频文件
            for file in os.listdir(output_dir):
                if file.lower().endswith('.mp4'):
                    file_path = os.path.join(output_dir, file)
                    # 如果是要处理的文件，加入原文件列表
                    if file in to_process_files:
                        original_files.append(file_path)
                    else:
                        # 如果不是要处理的文件，加入其他文件列表
                        other_files.append(file_path)

            # 如果找到原文件，显示在日志中并询问用户
            if original_files:
                self.update_frame_log("在输出目录中发现以下原文件：")
                for file in original_files:
                    self.update_frame_log(f"- {os.path.basename(file)}")

                msg = QMessageBox()
                msg.setIcon(QMessageBox.Question)
                msg.setWindowTitle("发现原文件")
                msg.setText("在输出目录中发现原文件，是否替换？")
                msg.setInformativeText(f"发现{len(original_files)}个原文件")
                replace_button = msg.addButton("替换", QMessageBox.YesRole)
                skip_button = msg.addButton("不替换", QMessageBox.NoRole)
                cancel_button = msg.addButton("取消", QMessageBox.RejectRole)
                msg.exec()

                clicked_button = msg.clickedButton()
                if clicked_button == cancel_button:
                    self.update_frame_log("⚠️ 用户取消操作")
                    return
                elif clicked_button == replace_button:
                    self.update_frame_log("✅ 用户选择替换原文件")
                    self.no_replace_mode = False
                    # 如果选择替换，将其他文件改为.MP
                    for file_path in other_files:
                        new_path = os.path.join(output_dir, os.path.splitext(os.path.basename(file_path))[0] + ".MP")
                        files_to_rename.append((file_path, new_path))
                else:
                    self.update_frame_log("✅ 用户选择不替换原文件")
                    self.no_replace_mode = True
                    # 如果选择不替换，只将其他文件（非目标文件）改为.MP
                    for file_path in other_files:
                        new_path = os.path.join(output_dir, os.path.splitext(os.path.basename(file_path))[0] + ".MP")
                        files_to_rename.append((file_path, new_path))
            else:
                # 如果没有找到原文件，将所有其他文件加入重命名列表
                self.no_replace_mode = False
                for file_path in other_files:
                    new_path = os.path.join(output_dir, os.path.splitext(os.path.basename(file_path))[0] + ".MP")
                    files_to_rename.append((file_path, new_path))

            # 如果有需要重命名的文件
            if files_to_rename:
                self.update_frame_log("\n以下文件将临时改为.MP：")
                for old_path, _ in files_to_rename:
                    self.update_frame_log(f"- {os.path.basename(old_path)}")

                # 执行重命名
                for old_path, new_path in files_to_rename:
                    try:
                        os.rename(old_path, new_path)
                        self.update_frame_log(f"✅ 重命名文件：{os.path.basename(old_path)} → {os.path.basename(new_path)}")
                    except Exception as e:
                        self.update_frame_log(f"❌ 重命名文件失败：{str(e)[:100]}")
                        # 恢复已经重命名的文件
                        for old_path, new_path in files_to_rename:
                            if os.path.exists(new_path):
                                try:
                                    os.rename(new_path, old_path)
                                except:
                                    pass
                        return

        # 初始化处理器
        from frame_removal import FrameRemovalProcessor
        self.frame_processor = FrameRemovalProcessor()
        self.frame_processor.log_signal.connect(self.update_frame_log)
        self.frame_processor.progress_signal.connect(self.update_frame_progress)

        # 设置参数
        if not replace_mode:
            self.frame_processor.set_output_directory(self.ui.DDCZ_lineEdit_3.text())
        self.frame_processor.set_frame_range(
            self.ui.DDCZ_spinBox_low_2.value(),
            self.ui.DDCZ_spinBox_max_2.value()
        )

        # 禁用按钮
        self.ui.DDCZ_pushButton_2.setEnabled(False)
        self.ui.DDCZ_pushButton_3.setEnabled(True)
        self.ui.DDCZprogressBar_2.setValue(0)

        # 开始处理
        try:
            # 根据用户选择设置replace_original参数
            if not replace_mode:
                # 如果是指定输出目录模式，根据用户选择决定是否替换
                should_replace = clicked_button == replace_button if 'clicked_button' in locals() else False
            else:
                # 如果是替换原素材模式，直接设为True
                should_replace = True

            processed_files = self.frame_processor.process_files(
                self.frame_files,
                replace_original=should_replace
            )

            if processed_files:
                # 如果启用了重命名功能，对处理后的文件进行重命名
                if self.ui.CMM_checkBox_5.isChecked():
                    processed_files = self.frame_rename_files(processed_files)
                QMessageBox.information(self, "成功", "抽帧处理完成！")
            else:
                QMessageBox.warning(self, "警告", "没有文件被成功处理！")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"处理过程中发生错误：{str(e)[:200]}")

        finally:
            # 恢复按钮状态
            self.ui.DDCZ_pushButton_2.setEnabled(True)
            self.ui.DDCZ_pushButton_3.setEnabled(False)

            # 恢复被重命名的文件
            if not replace_mode and files_to_rename:
                self.update_frame_log("\n正在恢复临时文件名...")
                for old_path, new_path in files_to_rename:
                    if os.path.exists(new_path):
                        try:
                            os.rename(new_path, old_path)
                            self.update_frame_log(f"✅ 恢复文件名：{os.path.basename(new_path)} → {os.path.basename(old_path)}")
                        except Exception as e:
                            self.update_frame_log(f"⚠️ 恢复文件名失败：{str(e)[:100]}")

    def stop_frame_removal(self):
        """停止抽帧处理"""
        if self.frame_processor:
            self.frame_processor.stop()
            self.ui.DDCZ_pushButton_2.setEnabled(True)
            self.ui.DDCZ_pushButton_3.setEnabled(False)
            QMessageBox.information(self, "提示", "已停止抽帧处理")

    def update_frame_log(self, message):
        """更新抽帧处理日志"""
        self.ui.DDCZ_plainTextEdit_2.appendPlainText(message)

    def update_frame_progress(self, current, total):
        """更新抽帧处理进度"""
        progress = int((current / total) * 100)
        self.ui.DDCZprogressBar_2.setValue(progress)

    def frame_rename_files(self, files):
        """对抽帧处理后的文件进行重命名

        Args:
            files: 要重命名的文件列表

        Returns:
            处理后的文件列表
        """
        if not files:
            return files

        # 获取重命名设置
        start_value = self.ui.CMM_spinBox_2.value()
        prefix = self.ui.CMM_lineEdit_3.text()
        suffix = self.ui.CMM_lineEdit_4.text()
        fill_zeros = self.ui.spinBox_2.value()
        keep_extension = self.ui.CMM_checkBox_5.isChecked()
        rename_enabled = self.ui.CMM_checkBox_4.isChecked()  # 是否启用批量重命名

        # 创建原始文件名到处理后文件名的映射
        original_names = {}
        for file in self.frame_files:
            original_name = os.path.splitext(os.path.basename(file))[0]
            original_names[original_name] = file

        processed_files = []
        total_files = len(files)

        self.update_frame_log("\n开始重命名处理...")
        for i, file in enumerate(files, start=start_value):
            try:
                # 获取文件路径信息
                file_dir = os.path.dirname(file)
                file_name = os.path.basename(file)

                if rename_enabled:
                    # 启用批量重命名时使用序号命名规则
                    base_name = f"{prefix}{str(i).zfill(fill_zeros)}{suffix}"

                    # 在不替换模式下，检查新名称是否与原文件名相同
                    if hasattr(self, 'no_replace_mode') and self.no_replace_mode:
                        # 查找对应的原始文件
                        processed_base = os.path.splitext(file_name)[0]
                        for orig_name in original_names.keys():
                            if processed_base.endswith(orig_name):
                                # 如果新名称与原文件名相同，添加_CZ后缀
                                if base_name == orig_name:
                                    base_name = f"{base_name}_CZ"
                                break
                else:
                    # 未启用批量重命名时保持原文件名
                    base_name = os.path.splitext(file_name)[0]
                    # 在不替换模式下添加_CZ后缀
                    if hasattr(self, 'no_replace_mode') and self.no_replace_mode:
                        base_name = f"{base_name}_CZ"

                # 添加扩展名
                if keep_extension:
                    new_name = base_name + os.path.splitext(file_name)[1]
                else:
                    new_name = base_name + ".mp4"

                new_path = os.path.join(file_dir, new_name)

                # 检查是否存在同名文件
                if os.path.exists(new_path) and new_path != file:
                    self.update_frame_log(f"⚠️ 文件已存在，跳过：{new_name}")
                    processed_files.append(file)
                    continue

                # 执行重命名
                os.rename(file, new_path)
                self.update_frame_log(f"✅ 重命名：{file_name} → {new_name}")
                processed_files.append(new_path)

            except Exception as e:
                self.update_frame_log(f"❌ 重命名失败：{str(e)[:100]}")
                processed_files.append(file)  # 保留原文件

            # 更新进度
            progress = int((i - start_value + 1) / total_files * 100)
            self.ui.DDCZprogressBar_2.setValue(progress)

        return processed_files

    # ===== tab_HJ 混剪功能实现 =====

    def add_hj_files(self):
        """添加混剪素材文件"""
        files, _ = QFileDialog.getOpenFileNames(
            self,
            "选择混剪素材文件",
            "",
            "视频文件 (*.mp4 *.avi *.mkv *.mov *.flv *.wmv)",
        )
        if files:
            self._add_hj_files(files)

    def _add_hj_files(self, files):
        """添加文件到混剪列表"""
        current_files = [f for f in self.hj_model.stringList()]
        new_files = []

        for file in files:
            file_name = Path(file).name
            if file_name not in current_files:
                new_files.append(file)
                current_files.append(file_name)

        if new_files:
            self.hj_files.extend(new_files)
            self.hj_model.setStringList(current_files)

    def remove_selected_hj_files(self):
        """移除选中的混剪文件"""
        indexes = self.ui.HJ_listView_4.selectedIndexes()
        if indexes:
            for idx in sorted(indexes, key=lambda x: x.row(), reverse=True):
                self.hj_files.pop(idx.row())
            self.hj_model.setStringList([Path(f).name for f in self.hj_files])

    def clear_hj_files(self):
        """清空混剪文件列表"""
        self.hj_files = []
        self.hj_model.setStringList([])

    def select_hj_output_directory(self):
        """选择混剪输出目录"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择输出目录")
        if dir_path:
            self.hj_output_dir = dir_path
            self.ui.HJSC_lineEdit_4.setText(dir_path)

    def toggle_hj_advanced_settings(self):
        """显示/隐藏高级设置"""
        is_visible = self.ui.HJ_groupBox_GJ.isVisible()
        self.ui.HJ_groupBox_GJ.setVisible(not is_visible)

        # 更新按钮文本
        if is_visible:
            self.ui.HJ_pushButton_GJ.setText("高级设置 ▼")
        else:
            self.ui.HJ_pushButton_GJ.setText("高级设置 ▲")

    def run_hj_initial_check(self):
        """运行混剪初始化检查"""
        if not self.hj_files:
            QMessageBox.warning(self, "错误", "请先添加混剪素材！")
            return
        if not self.hj_output_dir:
            QMessageBox.warning(self, "错误", "请先选择输出目录！")
            return

        # 检查视频分辨率和帧率
        self.update_hj_log("开始检查视频分辨率和帧率...")

        resolution_stats = {}
        framerate_stats = {}

        for file in self.hj_files:
            try:
                metadata = get_video_metadata(file)
                if metadata and 'streams' in metadata:
                    video_stream = metadata['streams'][0]
                    width = video_stream.get('width', 0)
                    height = video_stream.get('height', 0)

                    # 获取帧率
                    r_frame_rate = video_stream.get('r_frame_rate', '0/1')
                    if '/' in r_frame_rate:
                        num, den = map(int, r_frame_rate.split('/'))
                        fps = round(num / den, 2) if den != 0 else 0
                    else:
                        fps = float(r_frame_rate)

                    # 统计分辨率
                    resolution = f"{width}x{height}"
                    resolution_stats[resolution] = resolution_stats.get(resolution, 0) + 1

                    # 统计帧率
                    framerate_stats[fps] = framerate_stats.get(fps, 0) + 1

            except Exception as e:
                self.update_hj_log(f"❌ 检查文件失败：{Path(file).name} - {str(e)[:50]}")

        # 输出统计结果
        self.update_hj_log("\n=== 分辨率统计 ===")
        resolution_inconsistent = False
        if len(resolution_stats) > 1:
            resolution_inconsistent = True
            # 找出数量较少的分辨率
            sorted_resolutions = sorted(resolution_stats.items(), key=lambda x: x[1])
            minority_resolutions = [item for item in sorted_resolutions[:-1]]  # 除了最多的那个

            self.update_hj_log("❌ 发现分辨率不统一的文件：")
            for resolution, count in minority_resolutions:
                self.update_hj_log(f"  {resolution}: {count}个文件")
                # 列出具体文件
                for file in self.hj_files:
                    try:
                        metadata = get_video_metadata(file)
                        if metadata and 'streams' in metadata:
                            video_stream = metadata['streams'][0]
                            width = video_stream.get('width', 0)
                            height = video_stream.get('height', 0)
                            if f"{width}x{height}" == resolution:
                                self.update_hj_log(f"    - {Path(file).name}")
                    except:
                        pass
        else:
            self.update_hj_log("✅ 所有文件分辨率统一")

        self.update_hj_log("\n=== 帧率统计 ===")
        framerate_inconsistent = False
        if len(framerate_stats) > 1:
            framerate_inconsistent = True
            # 找出数量较少的帧率
            sorted_framerates = sorted(framerate_stats.items(), key=lambda x: x[1])
            minority_framerates = [item for item in sorted_framerates[:-1]]  # 除了最多的那个

            self.update_hj_log("❌ 发现帧率不统一的文件：")
            for framerate, count in minority_framerates:
                self.update_hj_log(f"  {framerate}fps: {count}个文件")
                # 列出具体文件
                for file in self.hj_files:
                    try:
                        metadata = get_video_metadata(file)
                        if metadata and 'streams' in metadata:
                            video_stream = metadata['streams'][0]
                            r_frame_rate = video_stream.get('r_frame_rate', '0/1')
                            if '/' in r_frame_rate:
                                num, den = map(int, r_frame_rate.split('/'))
                                fps = round(num / den, 2) if den != 0 else 0
                            else:
                                fps = float(r_frame_rate)
                            if fps == framerate:
                                self.update_hj_log(f"    - {Path(file).name}")
                    except:
                        pass
        else:
            self.update_hj_log("✅ 所有文件帧率统一")

        # 检查是否有不一致的情况
        if resolution_inconsistent or framerate_inconsistent:
            self.update_hj_log("\n❌ 检查完成！发现格式不一致，无法进行混剪")
            self.update_hj_log("请确保所有素材的分辨率和帧率完全一致后再进行混剪")

            # 显示警告对话框
            QMessageBox.critical(
                self,
                "格式不一致",
                "发现素材的分辨率或帧率不一致！\n\n"
                "为确保混剪质量，请使用分辨率和帧率完全一致的素材。\n"
                "详细信息请查看日志。"
            )
            return False
        else:
            self.update_hj_log("\n✅ 检查完成！所有素材格式一致，可以进行混剪")
            return True

    def update_hj_log(self, message):
        """更新混剪处理日志"""
        self.ui.CL_plainTextEdit_2.appendPlainText(message)

    # 落款功能
    def add_lk_files(self):
        """添加落款文件"""
        files, _ = QFileDialog.getOpenFileNames(
            self,
            "选择落款文件",
            "",
            "视频文件 (*.mp4 *.avi *.mkv *.mov *.flv *.wmv)",
        )
        if files:
            self._add_lk_files(files)

    def _add_lk_files(self, files):
        """添加文件到落款列表（只能有一个文件）"""
        if files:
            # 只取第一个文件
            file = files[0]
            self.lk_files = [file]
            self.lk_model.setStringList([Path(file).name])

    def clear_lk_files(self):
        """清空落款文件列表"""
        self.lk_files = []
        self.lk_model.setStringList([])

    def select_lk_folder(self):
        """选择落款文件夹"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择落款文件夹")
        if dir_path:
            self.ui.LK_lineEdit_3.setText(dir_path)

    def select_hj_music_folder(self):
        """选择tab_HJ的背景音乐文件夹"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择音乐文件夹")
        if dir_path:
            self.ui.YY_lineEdit_2.setText(dir_path)

    def on_lk_radio_toggled(self):
        """落款单选按钮切换"""
        if self.ui.LK_radioButton_duo.isChecked():
            # 多个落款模式
            self.ui.LK_lineEdit_3.setEnabled(True)
            self.ui.LK_pushButton_3.setEnabled(True)
            self.ui.LK_listView.setEnabled(False)
            self.ui.LK_in_5.setEnabled(False)
            self.ui.LK_clean_5.setEnabled(False)
        else:
            # 单个落款模式
            self.ui.LK_lineEdit_3.setEnabled(False)
            self.ui.LK_pushButton_3.setEnabled(False)
            self.ui.LK_listView.setEnabled(True)
            self.ui.LK_in_5.setEnabled(True)
            self.ui.LK_clean_5.setEnabled(True)

    def on_hj_transition_toggled(self):
        """转场功能切换"""
        enabled = self.ui.HJ_checkBox_ZC.isChecked()
        self.ui.HJ_comboBox_ZC.setEnabled(enabled)
        self.ui.ZC_doubleSpinBox_2.setEnabled(enabled)

        # 根据转场类型设置单选按钮状态
        self.on_hj_transition_changed()

    def on_hj_transition_changed(self):
        """转场类型改变"""
        transition_type = self.ui.HJ_comboBox_ZC.currentText()

        # 所有转场类型都支持多种/单一模式
        if self.ui.HJ_checkBox_ZC.isChecked():
            self.ui.HJ_radioButton_y.setEnabled(True)
            self.ui.HJ_radioButton_n.setEnabled(True)
        else:
            self.ui.HJ_radioButton_y.setEnabled(False)
            self.ui.HJ_radioButton_n.setEnabled(False)

    def start_hj_mix(self):
        """开始混剪处理"""
        if not self.hj_files:
            QMessageBox.warning(self, "错误", "请先添加混剪素材！")
            return
        if not self.hj_output_dir:
            QMessageBox.warning(self, "错误", "请先选择输出目录！")
            return

        # 执行初始化检查
        if not self.run_hj_initial_check():
            # 如果格式不一致，不允许开始混剪
            return

        # 检查落款设置
        if self.ui.HJ_checkBox_LK.isChecked():
            if self.ui.LK_radioButton_duo.isChecked():
                lk_folder = self.ui.LK_lineEdit_3.text().strip()
                if not lk_folder or not os.path.exists(lk_folder):
                    QMessageBox.warning(self, "错误", "请选择有效的落款文件夹！")
                    return
            else:
                if not self.lk_files:
                    QMessageBox.warning(self, "错误", "请添加落款文件！")
                    return

        self.hj_is_running = True
        self.ui.HJKS_pushButton_2.setEnabled(False)
        self.ui.HJTZ_pushButton_2.setEnabled(True)
        self.ui.HJ_progressBar_2.setValue(0)

        self.update_hj_log("开始混剪处理...")

        # 启动混剪线程
        self.hj_thread = HJMixingThread(self)
        self.hj_thread.progress_updated.connect(self.ui.HJ_progressBar_2.setValue)
        self.hj_thread.log_updated.connect(self.update_hj_log)
        self.hj_thread.finished.connect(self.on_hj_mix_finished)
        self.hj_thread.start()

    def stop_hj_mix(self):
        """停止混剪处理"""
        if hasattr(self, 'hj_thread') and self.hj_thread.isRunning():
            self.hj_thread.stop()
            self.update_hj_log("用户停止了混剪处理")

    def on_hj_mix_finished(self):
        """混剪处理完成"""
        self.hj_is_running = False
        self.ui.HJKS_pushButton_2.setEnabled(True)
        self.ui.HJTZ_pushButton_2.setEnabled(False)
        self.ui.HJ_progressBar_2.setValue(100)
        self.update_hj_log("混剪处理完成！")

    # ===== XSC_tab 洗素材功能实现 =====

    def add_xsc_files(self):
        """添加要洗的视频文件"""
        files, _ = QFileDialog.getOpenFileNames(
            self,
            "选择要洗的视频文件",
            "",
            "视频文件 (*.mp4 *.avi *.mkv *.mov *.flv *.wmv)",
        )
        if files:
            self._add_xsc_files(files)

    def _add_xsc_files(self, files):
        """添加文件到洗素材列表"""
        current_files = [f for f in self.xsc_model.stringList()]
        new_files = []

        for file in files:
            file_name = Path(file).name
            if file_name not in current_files:
                new_files.append(file)
                current_files.append(file_name)

        if new_files:
            self.xsc_files.extend(new_files)
            self.xsc_model.setStringList(current_files)

    def _add_sy_files(self, files):
        """添加文件到水印素材列表"""
        new_files = []

        for file in files:
            if file not in self.sy_files:
                new_files.append(file)

        if new_files:
            self.sy_files.extend(new_files)
            self.update_sy_list()

    def remove_selected_xsc_files(self):
        """移除选中的文件"""
        indexes = self.ui.XSC_listView_4.selectedIndexes()
        if indexes:
            for idx in sorted(indexes, key=lambda x: x.row(), reverse=True):
                self.xsc_files.pop(idx.row())
            self.xsc_model.setStringList([Path(f).name for f in self.xsc_files])

    def clear_xsc_files(self):
        """清空文件列表"""
        self.xsc_files = []
        self.xsc_model.setStringList([])

    def select_xsc_output_directory(self):
        """选择输出目录"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择输出目录")
        if dir_path:
            self.ui.XSC_lineEdit_4.setText(dir_path)

    def on_xsc_radio_toggled(self):
        """处理单选按钮状态改变"""
        replace_mode = self.ui.XSC_radioButton_TH_2.isChecked()
        # 禁用/启用相关控件
        self.ui.XSC_lineEdit_4.setEnabled(not replace_mode)
        self.ui.XSC_where_4.setEnabled(not replace_mode)

    def update_xsc_log(self, message):
        """更新洗素材日志"""
        self.ui.XSC_plainTextEdit_3RZ.appendPlainText(message)
        # 自动滚动到底部
        from PySide6.QtGui import QTextCursor
        cursor = self.ui.XSC_plainTextEdit_3RZ.textCursor()
        cursor.movePosition(QTextCursor.End)
        self.ui.XSC_plainTextEdit_3RZ.setTextCursor(cursor)

    def update_xsc_progress(self, value):
        """更新洗素材进度条"""
        self.ui.XSC_progressBar_3.setValue(value)

    def start_xsc_processing(self):
        """开始洗素材处理"""
        if not self.xsc_files:
            QMessageBox.warning(self, "错误", "请先添加要处理的视频文件！")
            return

        replace_mode = self.ui.XSC_radioButton_TH_2.isChecked()
        if not replace_mode and not self.ui.XSC_lineEdit_4.text():
            QMessageBox.warning(self, "错误", "请选择输出目录！")
            return

        # 获取目标分辨率和比特率
        try:
            target_width = int(self.ui.SXC_textEdit_W.toPlainText().strip())
            target_height = int(self.ui.SXC_textEdit_H.toPlainText().strip())
            target_bitrate = int(self.ui.SXC_textEdit_BTL.toPlainText().strip())

            if target_width <= 0 or target_height <= 0:
                raise ValueError("分辨率必须大于0")
            if target_bitrate <= 0:
                raise ValueError("比特率必须大于0")

        except ValueError as e:
            QMessageBox.warning(self, "参数错误", f"请检查分辨率和比特率设置：{str(e)}")
            return

        # 禁用按钮
        self.ui.XSC_pushButton_3.setEnabled(False)
        self.ui.XSC_pushButton_4.setEnabled(True)
        self.ui.XSC_progressBar_3.setValue(0)
        self.xsc_is_running = True

        # 清空日志
        self.ui.XSC_plainTextEdit_3RZ.clear()
        self.update_xsc_log("开始洗素材处理...")
        self.update_xsc_log(f"目标分辨率：{target_width}x{target_height}")
        self.update_xsc_log(f"目标比特率：{target_bitrate}kbps")
        self.update_xsc_log(f"帧率：30fps（固定）")

        # 创建并启动洗素材处理器
        from video_cleaner import VideoCleanerProcessor
        self.xsc_processor = VideoCleanerProcessor()
        self.xsc_processor.log_signal.connect(self.update_xsc_log)
        self.xsc_processor.progress_signal.connect(self.update_xsc_progress)
        self.xsc_processor.finished_signal.connect(self.on_xsc_processing_finished)

        # 设置参数
        if not replace_mode:
            self.xsc_processor.set_output_directory(self.ui.XSC_lineEdit_4.text())

        self.xsc_processor.set_target_resolution(target_width, target_height)
        self.xsc_processor.set_target_bitrate(target_bitrate)
        self.xsc_processor.set_target_framerate(30)  # 固定30fps

        # 开始处理
        self.xsc_processor.process_files(
            self.xsc_files,
            replace_original=replace_mode
        )

    def stop_xsc_processing(self):
        """停止洗素材处理"""
        if self.xsc_processor and self.xsc_is_running:
            self.xsc_processor.stop()
            self.update_xsc_log("用户停止了洗素材处理")

    def on_xsc_processing_finished(self):
        """洗素材处理完成"""
        self.xsc_is_running = False
        self.ui.XSC_pushButton_3.setEnabled(True)
        self.ui.XSC_pushButton_4.setEnabled(False)
        self.ui.XSC_progressBar_3.setValue(100)
        self.update_xsc_log("洗素材处理完成！")

    def toggle_interface_color(self, checked):
        """切换界面颜色模式"""
        self.is_dark_mode = checked
        if checked:
            self.apply_dark_theme()
        else:
            self.apply_light_theme()

    def apply_dark_theme(self):
        """应用黑白反色主题"""
        # 创建反色样式表
        dark_style = """
        /* 主窗口和基础控件 */
        QMainWindow, QWidget {
            background-color: #2b2b2b;
            color: #ffffff;
        }

        /* 标签页 - 直接针对主标签页控件 */
        QTabWidget::pane {
            border: 1px solid #555555;
            background-color: #2b2b2b;
        }

        /* 直接针对HJ_tab这个QTabWidget的标签页样式 */
        QTabWidget#HJ_tab::pane {
            border: 1px solid #555555 !important;
            background-color: #2b2b2b !important;
        }

        QTabWidget#HJ_tab QTabBar::tab {
            background-color: #2b2b2b !important;
            color: #ffffff !important;
            border: 1px solid #555555 !important;
            border-bottom: none !important;
            border-radius: 4px 4px 0 0 !important;
            padding: 8px 15px !important;
            margin-right: 2px !important;
            height: 30px !important;
            min-width: 100px !important;
            font-size: 14px !important;
        }

        QTabWidget#HJ_tab QTabBar::tab:selected {
            background-color: #48fba1 !important;
            color: #000000 !important;
            font-weight: bold !important;
        }

        QTabWidget#HJ_tab QTabBar::tab:hover:!selected {
            background-color: #404040 !important;
            color: #ffffff !important;
        }

        /* 分组框 */
        QGroupBox {
            color: #ffffff;
            border: 2px solid #555555;
            border-radius: 5px;
            margin-top: 1ex;
            font-weight: bold;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: #ffffff;
        }

        QGroupBox:disabled {
            color: #444444 !important;
            border: 2px solid #2a2a2a !important;
            opacity: 0.5;
        }

        QGroupBox::title:disabled {
            color: #444444 !important;
        }

        /* 标签 */
        QLabel {
            color: #ffffff;
            background-color: transparent;
        }

        /* 普通按钮（保持彩色按钮的原有颜色，只改变文字颜色） */
        QPushButton {
            color: #ffffff;
            border: 1px solid #555555;
            border-radius: 3px;
            padding: 5px;
        }

        /* 没有特殊颜色的按钮使用默认暗色背景 */
        QPushButton[objectName=""] {
            background-color: #404040;
        }

        QPushButton:hover {
            border: 2px solid #777777;
        }

        QPushButton:pressed {
            border: 2px solid #999999;
        }

        QPushButton:disabled {
            background-color: #1a1a1a !important;
            color: #444444 !important;
            border: 1px solid #2a2a2a !important;
            opacity: 0.5;
        }

        /* 输入框 */
        QLineEdit, QTextEdit, QPlainTextEdit {
            background-color: #404040;
            color: #ffffff;
            border: 1px solid #555555;
            border-radius: 3px;
            padding: 2px;
        }

        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
            border: 2px solid #0078d4;
        }

        QLineEdit:disabled, QTextEdit:disabled, QPlainTextEdit:disabled {
            background-color: #1a1a1a !important;
            color: #444444 !important;
            border: 1px solid #2a2a2a !important;
            opacity: 0.5;
        }

        /* 数字输入框 */
        QSpinBox, QDoubleSpinBox {
            background-color: #404040;
            color: #ffffff;
            border: 1px solid #555555;
            border-radius: 3px;
            padding: 2px;
            padding-right: 20px; /* 为箭头按钮留出空间 */
        }

        QSpinBox:focus, QDoubleSpinBox:focus {
            border: 2px solid #0078d4;
        }

        QSpinBox:disabled, QDoubleSpinBox:disabled {
            background-color: #1a1a1a !important;
            color: #444444 !important;
            border: 1px solid #2a2a2a !important;
            opacity: 0.5;
        }

        /* 上箭头按钮 */
        QSpinBox::up-button, QDoubleSpinBox::up-button {
            subcontrol-origin: border;
            subcontrol-position: top right;
            width: 18px;
            height: 12px;
            background-color: #505050;
            border: 1px solid #555555;
            border-bottom: none;
            border-top-right-radius: 3px;
        }

        QSpinBox::up-button:hover, QDoubleSpinBox::up-button:hover {
            background-color: #606060;
        }

        QSpinBox::up-button:pressed, QDoubleSpinBox::up-button:pressed {
            background-color: #707070;
        }

        /* 下箭头按钮 */
        QSpinBox::down-button, QDoubleSpinBox::down-button {
            subcontrol-origin: border;
            subcontrol-position: bottom right;
            width: 18px;
            height: 12px;
            background-color: #505050;
            border: 1px solid #555555;
            border-top: none;
            border-bottom-right-radius: 3px;
        }

        QSpinBox::down-button:hover, QDoubleSpinBox::down-button:hover {
            background-color: #606060;
        }

        QSpinBox::down-button:pressed, QDoubleSpinBox::down-button:pressed {
            background-color: #707070;
        }

        /* 禁用状态的箭头按钮 */
        QSpinBox::up-button:disabled, QSpinBox::down-button:disabled,
        QDoubleSpinBox::up-button:disabled, QDoubleSpinBox::down-button:disabled {
            background-color: #1a1a1a !important;
            border-color: #2a2a2a !important;
            opacity: 0.5;
        }

        /* 箭头图标 */
        QSpinBox::up-arrow, QDoubleSpinBox::up-arrow {
            image: none;
            width: 0;
            height: 0;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-bottom: 6px solid #ffffff;
        }

        QSpinBox::down-arrow, QDoubleSpinBox::down-arrow {
            image: none;
            width: 0;
            height: 0;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-top: 6px solid #ffffff;
        }

        QSpinBox::up-arrow:disabled, QSpinBox::down-arrow:disabled,
        QDoubleSpinBox::up-arrow:disabled, QDoubleSpinBox::down-arrow:disabled {
            border-top-color: #666666;
            border-bottom-color: #666666;
        }

        /* 下拉框 */
        QComboBox {
            background-color: #404040;
            color: #ffffff;
            border: 1px solid #555555;
            border-radius: 3px;
            padding: 2px;
        }

        QComboBox:focus {
            border: 2px solid #0078d4;
        }

        QComboBox::drop-down {
            border: none;
            background-color: #505050;
        }

        QComboBox::down-arrow {
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid #ffffff;
        }

        QComboBox QAbstractItemView {
            background-color: #404040;
            color: #ffffff;
            border: 1px solid #555555;
            selection-background-color: #0078d4;
        }

        QComboBox:disabled {
            background-color: #1a1a1a !important;
            color: #444444 !important;
            border: 1px solid #2a2a2a !important;
            opacity: 0.5;
        }

        QComboBox::drop-down:disabled {
            background-color: #1a1a1a !important;
            border-color: #2a2a2a !important;
        }

        /* 列表视图 */
        QListView {
            background-color: #404040;
            color: #ffffff;
            border: 1px solid #555555;
            border-radius: 3px;
        }

        QListView::item {
            padding: 2px;
            color: #ffffff;
        }

        QListView::item:selected {
            background-color: #0078d4;
            color: #ffffff;
        }

        QListView::item:hover {
            background-color: #505050;
        }

        QListView:disabled {
            background-color: #1a1a1a !important;
            color: #444444 !important;
            border: 1px solid #2a2a2a !important;
            opacity: 0.5;
        }

        QListView::item:disabled {
            color: #444444 !important;
            background-color: #1a1a1a !important;
        }

        /* 复选框 */
        QCheckBox {
            color: #ffffff;
            background-color: transparent;
        }

        QCheckBox::indicator {
            width: 13px;
            height: 13px;
            background-color: #404040;
            border: 1px solid #555555;
            border-radius: 2px;
        }

        QCheckBox::indicator:checked {
            background-color: #0078d4;
            border: 1px solid #0078d4;
            color: #ffffff;
        }

        QCheckBox::indicator:checked:hover {
            background-color: #106ebe;
        }

        QCheckBox:disabled {
            color: #444444 !important;
            opacity: 0.5;
        }

        QCheckBox::indicator:disabled {
            background-color: #1a1a1a !important;
            border: 1px solid #2a2a2a !important;
        }

        QCheckBox::indicator:checked:disabled {
            background-color: #2a4a5a !important;
            border: 1px solid #2a2a2a !important;
        }

        /* 单选按钮 */
        QRadioButton {
            color: #ffffff;
            background-color: transparent;
        }

        QRadioButton::indicator {
            width: 13px;
            height: 13px;
            border-radius: 7px;
            background-color: #404040;
            border: 1px solid #555555;
        }

        QRadioButton::indicator:checked {
            background-color: #0078d4;
            border: 1px solid #0078d4;
        }

        QRadioButton::indicator:checked:hover {
            background-color: #106ebe;
        }

        QRadioButton:disabled {
            color: #444444 !important;
            opacity: 0.5;
        }

        QRadioButton::indicator:disabled {
            background-color: #1a1a1a !important;
            border: 1px solid #2a2a2a !important;
        }

        QRadioButton::indicator:checked:disabled {
            background-color: #2a4a5a !important;
            border: 1px solid #2a2a2a !important;
        }

        /* 进度条 */
        QProgressBar {
            background-color: #404040;
            color: #ffffff;
            border: 1px solid #555555;
            border-radius: 3px;
            text-align: center;
        }

        QProgressBar::chunk {
            background-color: #0078d4;
            border-radius: 2px;
        }

        /* 分割线 */
        QFrame[frameShape="4"], QFrame[frameShape="5"] {
            color: #555555;
            background-color: #555555;
        }

        /* 菜单栏 */
        QMenuBar {
            background-color: #2b2b2b;
            color: #ffffff;
            border-bottom: 1px solid #555555;
        }

        QMenuBar::item {
            background-color: transparent;
            padding: 4px 8px;
            color: #ffffff;
        }

        QMenuBar::item:selected {
            background-color: #404040;
        }

        QMenu {
            background-color: #404040;
            color: #ffffff;
            border: 1px solid #555555;
        }

        QMenu::item {
            padding: 4px 20px;
            color: #ffffff;
        }

        QMenu::item:selected {
            background-color: #0078d4;
        }

        /* 状态栏 */
        QStatusBar {
            background-color: #2b2b2b;
            color: #ffffff;
            border-top: 1px solid #555555;
        }
        """

        # 应用样式表
        self.setStyleSheet(dark_style)

        # 特别处理彩色按钮，保持其原有颜色但改变文字颜色
        self._apply_colored_button_styles()

        # 直接覆盖主标签页的样式表
        self._apply_tab_dark_styles()

    def _apply_colored_button_styles(self):
        """为彩色按钮应用特殊样式，完全保持原有颜色不变"""
        # 定义所有彩色按钮及其原始样式
        colored_button_styles = """
        /* 浅蓝色按钮 (221, 255, 254, 179) */
        QPushButton#QT_in, QPushButton#HT_in, QPushButton#DDCZ_in_3,
        QPushButton#HJ_in_4, QPushButton#LK_in_5, QPushButton#XSC_in_4, QPushButton#SY_in_5 {
            background-color: rgba(221, 255, 254, 179);
            color: #000000;
            border: 1px solid #ccc;
            border-radius: 3px;
            padding: 5px;
        }
        QPushButton#QT_in:hover, QPushButton#HT_in:hover, QPushButton#DDCZ_in_3:hover,
        QPushButton#HJ_in_4:hover, QPushButton#LK_in_5:hover, QPushButton#XSC_in_4:hover, QPushButton#SY_in_5:hover {
            background-color: rgba(241, 255, 254, 200);
        }

        /* 黄色按钮 (255, 235, 15, 179) */
        QPushButton#SC_where, QPushButton#DDCZ_where_3, QPushButton#HJSC_where_4, QPushButton#XSC_where_4,
        QPushButton#SY_where_5, QPushButton#SY_where_6ZD {
            background-color: rgba(255, 235, 15, 179);
            color: #000000;
            border: 1px solid #ccc;
            border-radius: 3px;
            padding: 5px;
        }
        QPushButton#SC_where:hover, QPushButton#DDCZ_where_3:hover,
        QPushButton#HJSC_where_4:hover, QPushButton#XSC_where_4:hover,
        QPushButton#SY_where_5:hover, QPushButton#SY_where_6ZD:hover {
            background-color: rgba(255, 245, 35, 200);
        }

        /* 绿色按钮 (0, 255, 127, 179) */
        QPushButton#CSHJC, QPushButton#CSHJC_4 {
            background-color: rgba(0, 255, 127, 179);
            color: #000000;
            border: 1px solid #ccc;
            border-radius: 3px;
            padding: 5px;
            font-size: 11pt;
        }
        QPushButton#CSHJC:hover, QPushButton#CSHJC_4:hover {
            background-color: rgba(20, 255, 147, 200);
        }

        /* 青色按钮 (0, 255, 255, 179) */
        QPushButton#KS_pushButton, QPushButton#DDCZ_pushButton_2, QPushButton#HJKS_pushButton_2,
        QPushButton#XSC_pushButton_3, QPushButton#SY_pushButton_5 {
            background-color: rgba(0, 255, 255, 179);
            color: #000000;
            border: 1px solid #ccc;
            border-radius: 3px;
            padding: 5px;
            font-weight: bold;
        }
        QPushButton#KS_pushButton:hover, QPushButton#DDCZ_pushButton_2:hover,
        QPushButton#HJKS_pushButton_2:hover, QPushButton#XSC_pushButton_3:hover, QPushButton#SY_pushButton_5:hover {
            background-color: rgba(20, 255, 255, 200);
        }

        /* 红色按钮 (255, 0, 4, 179) */
        QPushButton#TZ_pushButton, QPushButton#DDCZ_pushButton_3, QPushButton#HJTZ_pushButton_2,
        QPushButton#XSC_pushButton_4, QPushButton#SY_pushButton_6 {
            background-color: rgba(255, 0, 4, 179);
            color: #ffffff;
            border: 1px solid #ccc;
            border-radius: 3px;
            padding: 5px;
            font-weight: bold;
        }
        QPushButton#TZ_pushButton:hover, QPushButton#DDCZ_pushButton_3:hover,
        QPushButton#HJTZ_pushButton_2:hover, QPushButton#XSC_pushButton_4:hover, QPushButton#SY_pushButton_6:hover {
            background-color: rgba(255, 20, 24, 200);
        }

        /* 紫色按钮 (255, 170, 255, 179) */
        QPushButton#YY_pushButton, QPushButton#YY_pushButton_2, QPushButton#LK_pushButton_3 {
            background-color: rgba(255, 170, 255, 179);
            color: #000000;
            border: 1px solid #ccc;
            border-radius: 3px;
            padding: 5px;
        }
        QPushButton#YY_pushButton:hover, QPushButton#YY_pushButton_2:hover, QPushButton#LK_pushButton_3:hover {
            background-color: rgba(255, 190, 255, 200);
        }

        /* 透明按钮 (255, 255, 255, 0) - 特殊处理 */
        QPushButton#HJ_pushButton_GJ {
            background-color: rgba(255, 255, 255, 0);
            color: rgba(170, 170, 255, 228);
            border: 1px solid #ccc;
            border-radius: 3px;
            padding: 5px;
        }
        QPushButton#HJ_pushButton_GJ:hover {
            background-color: rgba(240, 240, 240, 50);
        }

        /* 所有彩色按钮的禁用状态 */
        QPushButton#QT_in:disabled, QPushButton#HT_in:disabled, QPushButton#DDCZ_in_3:disabled,
        QPushButton#HJ_in_4:disabled, QPushButton#LK_in_5:disabled, QPushButton#XSC_in_4:disabled, QPushButton#SY_in_5:disabled,
        QPushButton#SC_where:disabled, QPushButton#DDCZ_where_3:disabled,
        QPushButton#HJSC_where_4:disabled, QPushButton#XSC_where_4:disabled,
        QPushButton#SY_where_5:disabled, QPushButton#SY_where_6ZD:disabled,
        QPushButton#CSHJC:disabled, QPushButton#CSHJC_4:disabled,
        QPushButton#KS_pushButton:disabled, QPushButton#DDCZ_pushButton_2:disabled,
        QPushButton#HJKS_pushButton_2:disabled, QPushButton#XSC_pushButton_3:disabled, QPushButton#SY_pushButton_5:disabled,
        QPushButton#TZ_pushButton:disabled, QPushButton#DDCZ_pushButton_3:disabled,
        QPushButton#HJTZ_pushButton_2:disabled, QPushButton#XSC_pushButton_4:disabled, QPushButton#SY_pushButton_6:disabled,
        QPushButton#YY_pushButton:disabled, QPushButton#YY_pushButton_2:disabled,
        QPushButton#LK_pushButton_3:disabled, QPushButton#HJ_pushButton_GJ:disabled {
            background-color: #1a1a1a !important;
            color: #444444 !important;
            border: 1px solid #2a2a2a !important;
            opacity: 0.5 !important;
        }
        """

        # 将所有彩色按钮样式添加到现有样式表
        current_style = self.styleSheet()
        self.setStyleSheet(current_style + colored_button_styles)

    def _apply_tab_dark_styles(self):
        """直接设置主标签页的暗色样式"""
        # 为主标签页设置暗色样式表，覆盖UI文件中的设置
        tab_dark_style = """
        /* 标签页基本样式（所有标签） */
        QTabBar::tab {
            height: 30px;
            min-width: 100px;
            padding: 8px 15px;
            border: 1px solid #555555;
            border-bottom: none;
            border-radius: 4px 4px 0 0;
            background: #2b2b2b;
            color: #ffffff;
            font-size: 14px;
        }

        /* 选中标签页的样式 */
        QTabBar::tab:selected {
            background: #48fba1;
            color: #000000;
            font-weight: bold;
        }

        /* 悬停效果（仅未选中的标签页） */
        QTabBar::tab:hover:!selected {
            background: #404040;
        }
        """

        # 直接设置主标签页控件的样式表
        self.ui.HJ_tab.setStyleSheet(tab_dark_style)

    def apply_light_theme(self):
        """应用默认浅色主题"""
        # 清除自定义样式，恢复默认外观
        self.setStyleSheet("")

        # 恢复主标签页的原始样式
        self._apply_tab_light_styles()

    def _apply_tab_light_styles(self):
        """恢复主标签页的原始浅色样式"""
        # 恢复UI文件中的原始样式
        original_tab_style = """
        /* 标签页基本样式（所有标签） */
        QTabBar::tab {
            height: 30px;
            min-width: 100px;
            padding: 8px 15px;
            border: 1px solid #ccc;
            border-bottom: none;
            border-radius: 4px 4px 0 0;
            background: #ffffff;
            font-size: 14px;
        }

        /* 选中标签页的样式 */
        QTabBar::tab:selected {
            background: #48fba1;
            font-weight: bold;
        }
        """

        # 恢复主标签页控件的原始样式表
        self.ui.HJ_tab.setStyleSheet(original_tab_style)

    def _validate_photo_resources(self):
        """验证photo文件夹资源"""
        try:
            is_valid, missing_items, error_message = validate_photo_structure()

            if not is_valid:
                # 显示警告但不阻止程序启动
                print(f"⚠️ Photo资源验证失败: {error_message}")
                print(f"缺失项目: {missing_items}")

                # 在开发环境下显示详细信息
                if not getattr(sys, 'frozen', False):
                    print("💡 请确保photo文件夹包含以下文件:")
                    print("  - HHlogo.png")
                    print("  - HHlogo整张水印.png")
                    print("  - others/ 文件夹（包含图片文件）")
            else:
                print("✅ Photo资源验证通过")

        except Exception as e:
            print(f"❌ Photo资源验证异常: {e}")

    # ===== tab_SY 水印工具方法 =====
    def add_sy_files(self):
        """添加水印素材文件"""
        files, _ = QFileDialog.getOpenFileNames(
            self,
            "选择视频文件",
            "",
            "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv *.flv *.webm)"
        )
        if files:
            for file in files:
                if file not in self.sy_files:
                    self.sy_files.append(file)
            self.update_sy_list()

    def remove_selected_sy_files(self):
        """移除选中的水印素材文件"""
        selected_indexes = self.ui.SY_listView_5.selectedIndexes()
        if selected_indexes:
            for index in sorted(selected_indexes, reverse=True):
                del self.sy_files[index.row()]
            self.update_sy_list()

    def clear_sy_files(self):
        """清空水印素材文件列表"""
        self.sy_files.clear()
        self.update_sy_list()

    def update_sy_list(self):
        """更新水印素材文件列表显示"""
        file_names = [os.path.basename(file) for file in self.sy_files]
        self.sy_model.setStringList(file_names)

    def select_sy_output_folder(self):
        """选择水印输出文件夹"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择输出文件夹")
        if dir_path:
            self.sy_output_dir = dir_path
            self.ui.SY_lineEdit_5.setText(dir_path)

    def select_sy_custom_folder(self):
        """选择自定义水印文件夹"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择水印图片文件夹")
        if dir_path:
            self.ui.SY_lineEdit_6ZD.setText(dir_path)
            self._save_settings()

    def on_sy_radio_toggled(self):
        """水印输出方式单选按钮切换"""
        if self.ui.SY_radioButton_TH_3.isChecked():
            # 替换原素材模式
            self.ui.SY_widget_SC_3.setEnabled(False)
        else:
            # 指定输出目录模式
            self.ui.SY_widget_SC_3.setEnabled(True)

    def on_sy_watermark_radio_toggled(self):
        """水印类型单选按钮切换"""
        # 检查是否选择了HHlogoJB（合合LOGO角标）
        if self.ui.SY_radioButton_HHlogoJB.isChecked():
            # 启用位置选择按钮
            self.ui.radioButton_LUP.setEnabled(True)
            self.ui.radioButton_RUP.setEnabled(True)
            self.ui.radioButton_LDW.setEnabled(True)
            self.ui.radioButton_RDW.setEnabled(True)
        else:
            # 禁用位置选择按钮
            self.ui.radioButton_LUP.setEnabled(False)
            self.ui.radioButton_RUP.setEnabled(False)
            self.ui.radioButton_LDW.setEnabled(False)
            self.ui.radioButton_RDW.setEnabled(False)

        # 检查是否选择了自定义水印
        if self.ui.SY_radioButton_ZD.isChecked():
            # 启用自定义设置
            self.ui.SY_lineEdit_6ZD.setEnabled(True)
            self.ui.SY_where_6ZD.setEnabled(True)
            self.ui.SY_spinBox_ZDSZ.setEnabled(True)
            self.ui.SY_label_ZDZD.setEnabled(True)
        else:
            # 禁用自定义设置
            self.ui.SY_lineEdit_6ZD.setEnabled(False)
            self.ui.SY_where_6ZD.setEnabled(False)
            self.ui.SY_spinBox_ZDSZ.setEnabled(False)
            self.ui.SY_label_ZDZD.setEnabled(False)

    def on_sy_position_radio_toggled(self):
        """位置选择单选按钮切换"""
        # 当选择位置时，自动选中HHlogoJB
        if (self.ui.radioButton_LUP.isChecked() or
            self.ui.radioButton_RUP.isChecked() or
            self.ui.radioButton_LDW.isChecked() or
            self.ui.radioButton_RDW.isChecked()):
            self.ui.SY_radioButton_HHlogoJB.setChecked(True)

    def start_sy_processing(self):
        """开始水印处理"""
        if not self.sy_files:
            QMessageBox.warning(self, "警告", "请先添加视频文件！")
            return

        # 检查输出设置
        if self.ui.SY_radioButton_ZD_3.isChecked():
            if not self.sy_output_dir:
                QMessageBox.warning(self, "警告", "请选择输出文件夹！")
                return

        # 检查水印设置（仅在非"无水印"模式下检查）
        if not self.ui.SY_radioButton_nothing.isChecked():
            if self.ui.SY_radioButton_ZD.isChecked():
                custom_folder = self.ui.SY_lineEdit_6ZD.text().strip()
                if not custom_folder or not os.path.exists(custom_folder):
                    QMessageBox.warning(self, "警告", "请选择有效的自定义水印文件夹！")
                    return

        # 验证视频分辨率
        invalid_videos = []
        for video_file in self.sy_files:
            try:
                metadata = get_video_metadata(video_file)
                if metadata and 'streams' in metadata:
                    video_stream = metadata['streams'][0]
                    width = video_stream.get('width', 0)
                    height = video_stream.get('height', 0)
                    if width != 1080 or height != 1920:
                        invalid_videos.append(f"{os.path.basename(video_file)} ({width}x{height})")
                else:
                    invalid_videos.append(f"{os.path.basename(video_file)} (无法获取分辨率)")
            except Exception as e:
                invalid_videos.append(f"{os.path.basename(video_file)} (解析失败: {str(e)})")

        if invalid_videos:
            QMessageBox.warning(
                self,
                "分辨率错误",
                f"以下视频不是1080*1920分辨率，无法处理：\n\n" + "\n".join(invalid_videos)
            )
            return

        # 开始处理
        self.sy_is_running = True
        self.ui.SY_pushButton_5.setEnabled(False)
        self.ui.SY_pushButton_6.setEnabled(True)
        self.ui.SY_progressBar_4.setValue(0)
        self.ui.SY_plainTextEdit_3RZ_2.clear()

        # 创建处理线程
        self.sy_processing_thread = SYWatermarkThread(self)
        self.sy_processing_thread.log_updated.connect(self.update_sy_log)
        self.sy_processing_thread.progress_updated.connect(self.ui.SY_progressBar_4.setValue)
        self.sy_processing_thread.finished.connect(self.on_sy_processing_finished)
        self.sy_processing_thread.start()

    def stop_sy_processing(self):
        """停止水印处理"""
        if self.sy_processing_thread and self.sy_processing_thread.isRunning():
            self.sy_processing_thread.stop()
            self.update_sy_log("⏹️ 用户请求停止处理...")

    def on_sy_processing_finished(self):
        """水印处理完成"""
        self.sy_is_running = False
        self.ui.SY_pushButton_5.setEnabled(True)
        self.ui.SY_pushButton_6.setEnabled(False)
        self.ui.SY_progressBar_4.setValue(100)
        self.update_sy_log("✅ 水印处理完成！")

    def update_sy_log(self, message):
        """更新水印处理日志"""
        self.ui.SY_plainTextEdit_3RZ_2.appendPlainText(message)
        # 自动滚动到底部
        cursor = self.ui.SY_plainTextEdit_3RZ_2.textCursor()
        cursor.movePosition(cursor.End)
        self.ui.SY_plainTextEdit_3RZ_2.setTextCursor(cursor)

    def get_sy_ffmpeg_path(self):
        """获取FFmpeg路径"""
        from pathlib import Path
        ffmpeg_paths = [
            Path("D:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffmpeg.exe"),
            Path("C:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffmpeg.exe"),
            Path("E:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffmpeg.exe"),
            Path("F:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffmpeg.exe")
        ]

        for path in ffmpeg_paths:
            if path.exists():
                return str(path)
        return None

class AutoWashMixingThread(QThread):
    """带自动洗素材功能的混剪线程"""
    log_signal = Signal(str)
    progress_signal = Signal(int, int)
    finished = Signal()

    def __init__(self, mixer, main_window):
        super().__init__()
        self.mixer = mixer
        self.main_window = main_window

    def run(self):
        try:
            # 设置运行状态
            self.mixer._is_running = True

            # 步骤1: 洗前贴素材
            self.log_signal.emit("🔄 步骤1: 开始洗前贴素材...")
            temp_main_dir = self._get_temp_dir("临时前贴")
            self.log_signal.emit(f"🔧 前贴素材数量: {len(self.mixer.main_files)}")
            for i, file in enumerate(self.mixer.main_files):
                self.log_signal.emit(f"🔧 前贴素材{i+1}: {file}")

            wash_result = self._wash_videos(self.mixer.main_files, temp_main_dir, "前贴")
            if not wash_result:
                self.log_signal.emit("❌ 前贴素材洗失败，停止处理")
                return

            # 步骤2: 洗后贴素材
            self.log_signal.emit("🔄 步骤2: 开始洗后贴素材...")
            temp_variant_dir = self._get_temp_dir("临时后贴")
            self.log_signal.emit(f"🔧 后贴素材数量: {len(self.mixer.variant_files)}")
            for i, file in enumerate(self.mixer.variant_files):
                self.log_signal.emit(f"🔧 后贴素材{i+1}: {file}")

            wash_result = self._wash_videos(self.mixer.variant_files, temp_variant_dir, "后贴")
            if not wash_result:
                self.log_signal.emit("❌ 后贴素材洗失败，停止处理")
                return

            # 步骤3: 使用洗好的素材进行混剪
            self.log_signal.emit("🔄 步骤3: 开始混剪...")
            original_main_files = self.mixer.main_files.copy()
            original_variant_files = self.mixer.variant_files.copy()

            # 替换为洗好的素材
            self.mixer.main_files = self._get_washed_files(temp_main_dir)
            self.mixer.variant_files = self._get_washed_files(temp_variant_dir)

            # 执行混剪
            mixing_thread = MixingThread(self.mixer)
            mixing_thread.log_signal.connect(self.log_signal.emit)
            mixing_thread.progress_signal.connect(self.progress_signal.emit)
            mixing_thread.run()  # 直接运行，不启动新线程

            # 步骤4: 对成品文件进行再次洗素材
            self.log_signal.emit("🔄 步骤4: 对成品文件进行再次洗素材...")
            # 确保运行状态为True，以便洗成品功能正常执行
            self.mixer._is_running = True
            output_files = self._get_output_files()
            if output_files:
                self._wash_output_files(output_files)

            # 步骤5: 清理临时文件
            self.log_signal.emit("🔄 步骤5: 清理临时文件...")
            self._cleanup_temp_dirs(temp_main_dir, temp_variant_dir)

            # 恢复原始文件列表
            self.mixer.main_files = original_main_files
            self.mixer.variant_files = original_variant_files

            self.log_signal.emit("✅ 竖版素材自动洗混剪完成！")

        except Exception as e:
            self.log_signal.emit(f"❌ 自动洗混剪失败：{str(e)}")
            # 即使出错也要清理临时文件
            try:
                if 'temp_main_dir' in locals():
                    self._cleanup_temp_dirs(temp_main_dir, temp_variant_dir)
            except:
                pass
        finally:
            # 重置运行状态
            self.mixer._is_running = False
            self.finished.emit()

    def _get_temp_dir(self, folder_name):
        """获取临时目录路径"""
        from pathlib import Path
        # 获取FFmpeg路径的父目录
        ffmpeg_dir = Path(self.mixer.ffmpeg_path).parent
        temp_dir = ffmpeg_dir / folder_name
        temp_dir.mkdir(exist_ok=True)
        return temp_dir

    def _wash_videos(self, video_files, output_dir, file_type):
        """洗素材"""
        try:
            from video_cleaner import VideoCleanerProcessor
            from pathlib import Path

            self.log_signal.emit(f"🔧 开始洗{file_type}素材，共{len(video_files)}个文件")
            self.log_signal.emit(f"🔧 输出目录: {output_dir}")

            processor = VideoCleanerProcessor()
            processor.set_output_directory(str(output_dir))
            processor.set_target_resolution(1080, 1920)
            processor.set_target_bitrate(12000)
            processor.set_target_framerate(30)
            processor.replace_original = False  # 设置为输出到指定目录模式

            self.log_signal.emit(f"🔧 VideoCleanerProcessor配置完成")

            # 连接信号
            processor.log_signal.connect(self.log_signal.emit)

            # 同步处理
            total = len(video_files)
            for i, video_file in enumerate(video_files):
                if not self.mixer._is_running:
                    self.log_signal.emit(f"⚠️ 混剪已停止，中断洗{file_type}素材")
                    return False

                # 检查文件是否存在
                if not Path(video_file).exists():
                    self.log_signal.emit(f"❌ 文件不存在: {video_file}")
                    return False

                file_size = Path(video_file).stat().st_size
                self.log_signal.emit(f"正在洗{file_type}素材 ({i+1}/{total}): {Path(video_file).name} (大小: {file_size} 字节)")
                self.log_signal.emit(f"🔧 输入文件完整路径: {video_file}")

                success = processor._process_single_file(str(video_file))
                if not success:
                    self.log_signal.emit(f"❌ 洗{file_type}素材失败: {Path(video_file).name}")
                    return False
                else:
                    self.log_signal.emit(f"✅ {file_type}素材洗完成: {Path(video_file).name}")

                self.progress_signal.emit(i+1, total*4)  # 总共4个步骤

            self.log_signal.emit(f"✅ 所有{file_type}素材洗完成")
            return True

        except Exception as e:
            self.log_signal.emit(f"❌ 洗{file_type}素材失败：{str(e)}")
            import traceback
            self.log_signal.emit(f"🔧 详细错误: {traceback.format_exc()}")
            return False

    def _get_washed_files(self, temp_dir):
        """获取洗好的文件列表"""
        from pathlib import Path
        washed_files = []

        self.log_signal.emit(f"🔧 检查洗好的文件，目录: {temp_dir}")

        if not temp_dir.exists():
            self.log_signal.emit(f"❌ 临时目录不存在: {temp_dir}")
            return washed_files

        all_files = list(temp_dir.glob("*"))
        self.log_signal.emit(f"🔧 临时目录中的所有文件: {len(all_files)} 个")

        for file in all_files:
            self.log_signal.emit(f"🔧 发现文件: {file.name}")

        for file in temp_dir.glob("*.mp4"):
            washed_files.append(file)
            self.log_signal.emit(f"✅ 找到洗好的文件: {file.name}")

        self.log_signal.emit(f"🔧 总共找到 {len(washed_files)} 个洗好的文件")
        return washed_files

    def _get_output_files(self):
        """获取混剪输出的文件列表"""
        output_files = []
        output_dir = self.mixer.output_dir

        self.log_signal.emit(f"🔧 检查混剪输出文件，目录: {output_dir}")

        if not output_dir.exists():
            self.log_signal.emit(f"❌ 输出目录不存在: {output_dir}")
            return output_files

        all_files = list(output_dir.glob("*"))
        self.log_signal.emit(f"🔧 输出目录中的所有文件: {len(all_files)} 个")

        for file in all_files:
            self.log_signal.emit(f"🔧 发现文件: {file.name} (大小: {file.stat().st_size if file.is_file() else '目录'})")

        for file in output_dir.glob("*.mp4"):
            output_files.append(file)
            self.log_signal.emit(f"✅ 找到混剪输出文件: {file.name}")

        self.log_signal.emit(f"🔧 总共找到 {len(output_files)} 个混剪输出文件")
        return output_files

    def _wash_output_files(self, output_files):
        """对成品文件进行再次洗素材"""
        try:
            from video_cleaner import VideoCleanerProcessor
            from pathlib import Path

            self.log_signal.emit(f"🔧 开始洗成品文件，共{len(output_files)}个文件")

            processor = VideoCleanerProcessor()
            processor.set_target_resolution(1080, 1920)
            processor.set_target_bitrate(12000)
            processor.set_target_framerate(30)
            processor.replace_original = True  # 设置为替换原文件模式

            # 连接信号
            processor.log_signal.connect(self.log_signal.emit)

            self.log_signal.emit(f"🔧 VideoCleanerProcessor配置完成（替换原文件模式）")

            total = len(output_files)
            for i, output_file in enumerate(output_files):
                if not self.mixer._is_running:
                    self.log_signal.emit(f"⚠️ 混剪已停止，中断洗成品文件")
                    return

                # 检查文件是否存在
                if not output_file.exists():
                    self.log_signal.emit(f"❌ 成品文件不存在: {output_file}")
                    continue

                file_size = output_file.stat().st_size
                self.log_signal.emit(f"正在洗成品文件 ({i+1}/{total}): {output_file.name} (大小: {file_size} 字节)")
                self.log_signal.emit(f"🔧 成品文件完整路径: {output_file}")

                # 替换原文件模式
                success = processor._process_single_file(str(output_file))
                if success:
                    new_size = output_file.stat().st_size if output_file.exists() else 0
                    self.log_signal.emit(f"✅ 成品文件洗完成: {output_file.name} (新大小: {new_size} 字节)")
                else:
                    self.log_signal.emit(f"❌ 成品文件洗失败: {output_file.name}")

                self.progress_signal.emit(total*3 + i+1, total*4)  # 第4步

            self.log_signal.emit(f"✅ 所有成品文件洗完成")

        except Exception as e:
            self.log_signal.emit(f"❌ 洗成品文件失败：{str(e)}")
            import traceback
            self.log_signal.emit(f"🔧 详细错误: {traceback.format_exc()}")

    def _cleanup_temp_dirs(self, temp_main_dir, temp_variant_dir):
        """清理临时文件夹和所有临时文件"""
        import shutil

        try:
            # 删除临时前贴文件夹
            if temp_main_dir.exists():
                files_in_dir = list(temp_main_dir.glob("*"))
                self.log_signal.emit(f"🔧 临时前贴文件夹中有 {len(files_in_dir)} 个文件")
                shutil.rmtree(temp_main_dir)
                self.log_signal.emit(f"✅ 已删除临时前贴文件夹: {temp_main_dir}")

            # 删除临时后贴文件夹
            if temp_variant_dir.exists():
                files_in_dir = list(temp_variant_dir.glob("*"))
                self.log_signal.emit(f"🔧 临时后贴文件夹中有 {len(files_in_dir)} 个文件")
                shutil.rmtree(temp_variant_dir)
                self.log_signal.emit(f"✅ 已删除临时后贴文件夹: {temp_variant_dir}")

            # 清理输出目录中的临时文件
            self._cleanup_output_temp_files()

        except Exception as e:
            self.log_signal.emit(f"⚠️ 清理临时文件失败：{str(e)}")
            # 清理失败不影响主流程

    def _cleanup_output_temp_files(self):
        """清理输出目录中的临时文件"""
        try:
            output_dir = self.mixer.output_dir
            if not output_dir.exists():
                return

            # 先列出所有文件用于调试
            all_files = list(output_dir.glob("*"))
            self.log_signal.emit(f"🔧 输出目录中的所有文件: {[f.name for f in all_files]}")

            # 查找所有可能的临时文件
            temp_patterns = [
                "*_temp.mp4",      # VideoCleanerProcessor生成的临时文件
                "*_temp",          # 没有扩展名的临时文件
                "temp_*",          # 其他临时文件
                "*_cleaned_temp*", # 清理过程中的临时文件
                "one_front_once_*.mp4",  # 混剪生成的中间文件
            ]

            temp_files_found = []
            for pattern in temp_patterns:
                temp_files = list(output_dir.glob(pattern))
                if temp_files:
                    self.log_signal.emit(f"🔧 模式 '{pattern}' 匹配到 {len(temp_files)} 个文件: {[f.name for f in temp_files]}")
                temp_files_found.extend(temp_files)

            if temp_files_found:
                self.log_signal.emit(f"🔧 在输出目录中发现 {len(temp_files_found)} 个临时文件")
                for temp_file in temp_files_found:
                    try:
                        if temp_file.is_file():
                            temp_file.unlink()
                            self.log_signal.emit(f"✅ 已删除临时文件: {temp_file.name}")
                        elif temp_file.is_dir():
                            import shutil
                            shutil.rmtree(temp_file)
                            self.log_signal.emit(f"✅ 已删除临时目录: {temp_file.name}")
                    except Exception as e:
                        self.log_signal.emit(f"⚠️ 删除临时文件失败 {temp_file.name}: {str(e)}")
            else:
                self.log_signal.emit("✅ 输出目录中没有发现临时文件")

        except Exception as e:
            self.log_signal.emit(f"⚠️ 清理输出目录临时文件失败：{str(e)}")


def main_application():
    """主应用程序函数"""
    app = QApplication(sys.argv)

    # 设置全局中文字体
    font = app.font()
    font.setFamily("SimHei")
    app.setFont(font)

    window = MainWindow()
    window.show()
    return app.exec()

def check_authorization_with_optimization():
    """优化的授权检查，保持完整验证但优化性能"""
    try:
        # 延迟导入授权模块，避免启动时加载重模块
        from machine_code_verifier import check_authorization_only

        # 进行完整的授权检查（包含时间验证）
        valid, _, _, _ = check_authorization_only("MFChen视频混剪工具")

        if valid:
            return True, None
        else:
            # 只有在需要时才导入GUI相关模块
            from machine_code_verifier import run_application_with_authorization_check
            return False, lambda: run_application_with_authorization_check(lambda: None, "MFChen视频混剪工具")
    except ImportError:
        # 开发环境，跳过授权检查
        return True, None

if __name__ == "__main__":
    # 优化的授权检查（保持完整验证）
    auth_valid, auth_func = check_authorization_with_optimization()

    if auth_valid:
        # 授权通过，直接启动主程序
        sys.exit(main_application())
    else:
        # 授权失败，显示授权对话框
        sys.exit(auth_func())




